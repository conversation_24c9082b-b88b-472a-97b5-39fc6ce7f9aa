"""
此脚本用于纵向合并两个环境监测数据文件：
1. transformed_24环境数据3303A.csv (2024年数据)
2. transformed_环境数据3303A.csv (2023年数据)

合并过程包括：
- 读取两个CSV文件
- 纵向合并数据框
- 将时间字符串转换为datetime格式
- 按时间顺序排序
- 重置索引
- 保存合并后的数据到新文件
- 输出合并后数据的基本统计信息

输出文件：merged_vertical_data.csv
"""

import pandas as pd
import os
import sys

try:
    # 检查输出文件是否被占用
    output_file = "file/merged_vertical_data.csv"
    if os.path.exists(output_file):
        try:
            # 尝试打开文件
            with open(output_file, "a"):
                pass
        except PermissionError:
            print(f"错误：文件 {output_file} 可能正在被其他程序使用。")
            print("请关闭可能正在使用该文件的程序（如Excel）后重试。")
            sys.exit(1)

    # 读取两个CSV文件
    df_2024 = pd.read_csv("file/transformed_24环境数据3303A.csv")
    df_2023 = pd.read_csv("file/transformed_环境数据3303A.csv")

    # 将两个数据框纵向合并
    df_merged = pd.concat([df_2023, df_2024], axis=0)

    # 按时间排序
    df_merged["datetime"] = pd.to_datetime(df_merged["datetime"])
    df_merged = df_merged.sort_values("datetime")

    # 重置索引
    df_merged = df_merged.reset_index(drop=True)

    # 保存合并后的文件
    df_merged.to_csv(output_file, index=False)

    # 打印基本信息
    print("\n合并后的数据基本信息：")
    print(f"总行数: {len(df_merged)}")
    print(f"时间范围: {df_merged['datetime'].min()} 到 {df_merged['datetime'].max()}")
    print("\n每个指标的非空值数量：")
    print(df_merged.count())

except PermissionError as e:
    print(f"\n错误：无法访问或写入文件。")
    print("可能的原因：")
    print("1. 文件正在被其他程序使用")
    print("2. 没有文件夹的写入权限")
    print("3. 文件被设置为只读")
    print("\n请检查以上问题后重试。")
    sys.exit(1)
except Exception as e:
    print(f"\n发生错误：{str(e)}")
    sys.exit(1)
