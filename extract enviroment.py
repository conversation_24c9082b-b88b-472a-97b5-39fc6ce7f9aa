import pandas as pd
import os
from pathlib import Path


def process_csv_files():
    # 设置文件路径
    source_dir = "站点_20230101-20231231"
    target_file = "环境数据3303A.csv"

    # 获取目录下所有CSV文件
    csv_files = list(Path(source_dir).glob("*.csv"))

    # 用于存储所有处理后的数据
    all_data = []

    # 处理每个CSV文件
    for csv_file in csv_files:
        try:
            # 读取CSV文件
            df = pd.read_csv(csv_file)

            # 提取站点代码为3303A的数据
            if "3303A" in df.columns:
                # 选择需要的列
                selected_columns = ["date", "hour", "type", "3303A"]
                df_selected = df[selected_columns]

                # 将数据添加到列表中
                all_data.append(df_selected)

        except Exception as e:
            print(f"处理文件 {csv_file} 时出错: {str(e)}")

    if all_data:
        # 合并所有数据
        final_df = pd.concat(all_data, ignore_index=True)

        # 保存处理后的数据
        final_df.to_csv(target_file, index=False)
        print(f"数据已保存到: {target_file}")

        # 转换数据形状
        transformed_df = transform_data_shape(target_file)
        transformed_file = "transformed_环境数据3303A.csv"
        transformed_df.to_csv(transformed_file, index=True)
        print(f"转换后的数据已保存到: {transformed_file}")
    else:
        print("未找到符合条件的站点数据")


def transform_data_shape(input_file):
    """
    将数据转换为新的形状，每个时间点对应一行，不同的环境参数作为列
    """
    # 读取CSV文件
    df = pd.read_csv(input_file)

    # 确保date和hour列都是字符串类型
    df["date"] = df["date"].astype(str)
    df["hour"] = df["hour"].astype(str).str.zfill(2)

    # 创建时间列
    df["datetime"] = pd.to_datetime(df["date"] + " " + df["hour"] + ":00:00")

    # 数据透视表转换
    pivot_df = df.pivot(index="datetime", columns="type", values="3303A")

    # 确保所有列名都是字符串类型
    pivot_df.columns = pivot_df.columns.astype(str)

    # 按时间顺序排序
    pivot_df.sort_index(inplace=True)

    return pivot_df


if __name__ == "__main__":
    process_csv_files()
