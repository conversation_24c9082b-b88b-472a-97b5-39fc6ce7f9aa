# 数据处理流程文档

## 文件结构
本项目包含三个主要的Python脚本文件，用于数据的处理、转换和清洗：
- `transform_gui.py`：数据转换的图形界面程序
- `process_data.py`：数据处理和合并脚本
- `clean_merged_data.py`：数据清洗和标准化脚本

## 数据处理流程

### 1. 数据转换 (transform_gui.py)
该脚本提供了一个图形用户界面，用于数据的初始转换处理。

#### 主要功能
1. 提供友好的图形界面，支持高DPI显示
2. 允许用户：
   - 输入站点代码（默认：3303A）
   - 选择输入目录（包含CSV文件的文件夹）
   - 选择输出目录
3. 数据转换功能：
   - 读取指定目录下的所有CSV文件
   - 提取指定站点代码的数据
   - 将数据转换为新的形状（每个时间点对应一行，不同环境参数作为列）
   - 合并处理多个CSV文件的数据

#### 界面组件
- 站点代码输入框
- 输入/输出目录选择框和浏览按钮
- 进度条显示处理进度
- 状态标签显示当前处理状态
- 开始转换按钮

#### 数据处理逻辑
```python
def transform_data_shape(self, input_file):
    # 读取CSV文件
    df = pd.read_csv(input_file)
    
    # 创建时间列
    df['datetime'] = pd.to_datetime(df['date'] + ' ' + df['hour'] + ':00:00')
    
    # 数据透视表转换
    pivot_df = df.pivot(index='datetime', columns='type', values=station_code)
    
    # 按时间顺序排序
    pivot_df.sort_index(inplace=True)
```

### 2. 数据处理和合并 (process_data.py)
该脚本主要完成以下功能：
1. 读取环境监测数据（tyd2311-2312.csv）
2. 将数据按小时进行采样
   - 数值型数据使用平均值
   - 风向数据使用众数
3. 读取空气质量数据（3303A2311-2312.csv）
4. 基于时间戳合并两个数据集
5. 生成合并后的数据文件（merged_data_3303_env.csv）

主要处理步骤：
```python
# 读取环境数据
df_env = pd.read_csv('file/tyd2311-2312.csv')

# 时间戳处理
df_env['time_points'] = pd.to_datetime(df_env['time_points'])
df_env['hour'] = df_env['time_points'].dt.floor('h')

# 按小时分组计算统计值
hourly_env_data = df_env.groupby('hour')[mean_columns].mean()

# 合并数据集
merged_data = pd.merge(df_3303, hourly_env_data, on='datetime', how='left')
```

### 3. 数据清洗 (clean_merged_data.py)
该脚本执行数据清洗和标准化处理：

1. 数据读取和初始化
```python
df = pd.read_csv('file/merged_data_3303_env.csv')
df['datetime'] = pd.to_datetime(df['datetime'])
```

2. 列名标准化
```python
columns_to_rename = {
    'CO_x': 'CO_env',
    'NO2_x': 'NO2_env',
    'O3_x': 'O3_env',
    'PM10_x': 'PM10_env',
    'PM2.5': 'PM2.5_3303',
    'SO2_x': 'SO2_env',
    'SO2_y': 'SO2_env2',
    'PM10_y': 'PM10_env2',
    'PM2_5': 'PM2.5_env2',
    'O3_y': 'O3_env2'
}
```

3. 缺失值处理
- 使用线性插值填充连续数据
- 使用前向/后向填充处理剩余缺失值
- 最后用0填充任何剩余的缺失值

4. 异常值处理
- 使用3个标准差（3σ）作为阈值
- 对非常数列进行异常值截断

5. 数据类型统一
- datetime列：datetime64[ns]
- 数值列：float64

### 输出文件
最终生成的清洗后数据文件：`cleaned_merged_data.csv`

数据特征：
- 行数：1217
- 列数：33（1个时间列 + 32个数值列）
- 时间范围：2023-11-11 00:00:00 至 2023-12-31 23:00:00
- 所有缺失值已处理
- 异常值已被限制在合理范围内

## 数据字段说明

### 环境监测数据字段
- temp：温度
- humi：湿度
- airpress：气压
- wind_speed：风速
- wind_direction：风向
- rain：降雨量
- rain_24hour：24小时降雨量

### 空气质量数据字段
- AQI：空气质量指数
- CO_env：一氧化碳浓度
- NO2_env：二氧化氮浓度
- O3_env：臭氧浓度
- PM10_env：PM10浓度
- PM2.5_3303：PM2.5浓度
- SO2_env：二氧化硫浓度

### 24小时平均值字段
- CO_24h：CO 24小时平均值
- NO2_24h：NO2 24小时平均值
- O3_24h：O3 24小时平均值
- PM10_24h：PM10 24小时平均值
- PM2.5_24h：PM2.5 24小时平均值
- SO2_24h：SO2 24小时平均值 