{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 增强版腐蚀电流影响分析\n", "\n", "使用所有环境特征分析对腐蚀电流(i1)的影响，包含缺失值处理和模型优化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入所有必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.preprocessing import PolynomialFeatures, StandardScaler\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.impute import SimpleImputer, KNNImputer\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"所有库导入成功！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载和详细分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取数据\n", "df = pd.read_csv('file/merged_hourly_data.csv')\n", "\n", "print(\"=\"*60)\n", "print(\"数据基本信息:\")\n", "print(\"=\"*60)\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"时间范围: {df['time_points'].min()} 到 {df['time_points'].max()}\")\n", "\n", "# 定义特征列（排除时间列）\n", "feature_cols = [col for col in df.columns if col not in ['time_points', 'i1']]\n", "target_col = 'i1'\n", "\n", "print(f\"\\n环境特征列 ({len(feature_cols)}个):\")\n", "for i, col in enumerate(feature_cols, 1):\n", "    print(f\"{i:2d}. {col}\")\n", "\n", "print(f\"\\n目标变量: {target_col}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 缺失值分析\n", "print(\"=\"*60)\n", "print(\"缺失值分析:\")\n", "print(\"=\"*60)\n", "missing_stats = df.isnull().sum()\n", "missing_pct = (missing_stats / len(df)) * 100\n", "\n", "missing_df = pd.DataFrame({\n", "    '缺失数量': missing_stats,\n", "    '缺失百分比': missing_pct\n", "}).sort_values('缺失数量', ascending=False)\n", "\n", "display(missing_df[missing_df['缺失数量'] > 0])\n", "\n", "# 数据分布分析\n", "print(\"\\n数据统计摘要:\")\n", "display(df[feature_cols + [target_col]].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 缺失值处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 处理缺失值 - 使用KNN插补\n", "print(\"正在使用KNN方法处理缺失值...\")\n", "\n", "# 首先删除目标变量为空的行\n", "df_clean = df.dropna(subset=[target_col]).copy()\n", "print(f\"删除目标变量缺失行后，数据形状: {df_clean.shape}\")\n", "\n", "# 使用KNN插补处理特征列的缺失值\n", "imputer = KNNImputer(n_neighbors=5)\n", "X_features = df_clean[feature_cols]\n", "X_imputed = imputer.fit_transform(X_features)\n", "\n", "# 创建新的DataFrame\n", "df_imputed = df_clean.copy()\n", "df_imputed[feature_cols] = X_imputed\n", "\n", "print(f\"插补后的数据形状: {df_imputed.shape}\")\n", "print(f\"插补后缺失值检查: {df_imputed[feature_cols + [target_col]].isnull().sum().sum()}\")\n", "\n", "# 使用插补后的数据\n", "df_clean = df_imputed"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 特征相关性分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算与目标变量的相关性\n", "correlations = df_clean[feature_cols + [target_col]].corr()[target_col].drop(target_col)\n", "correlations_abs = correlations.abs().sort_values(ascending=False)\n", "\n", "print(\"与腐蚀电流(i1)的相关性排序:\")\n", "print(\"=\"*50)\n", "for feature, corr in correlations_abs.items():\n", "    print(f\"{feature:<15}: {correlations[feature]:>8.4f} (|{corr:.4f}|)\")\n", "\n", "# 选择高相关性特征\n", "high_corr_features = correlations_abs[correlations_abs > 0.1].index.tolist()\n", "print(f\"\\n高相关性特征 (|相关系数| > 0.1): {len(high_corr_features)}个\")\n", "print(high_corr_features)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制相关性热力图\n", "plt.figure(figsize=(12, 10))\n", "correlation_matrix = df_clean[feature_cols + [target_col]].corr()\n", "\n", "# 创建掩码，只显示上三角\n", "mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))\n", "\n", "sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,\n", "            square=True, linewidths=0.5, cbar_kws={\"shrink\": .8}, fmt='.3f')\n", "plt.title('特征相关性矩阵')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 特征关系可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化前6个重要特征与目标变量的关系\n", "top_n = 6\n", "top_features = correlations_abs.head(top_n).index.tolist()\n", "\n", "# 创建子图\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.ravel()\n", "\n", "for i, feature in enumerate(top_features):\n", "    if i < len(axes):\n", "        axes[i].scatter(df_clean[feature], df_clean[target_col], alpha=0.6, s=20)\n", "        axes[i].set_xlabel(feature)\n", "        axes[i].set_ylabel('腐蚀电流 i1')\n", "        axes[i].set_title(f'{feature} vs 腐蚀电流\\n相关系数: {correlations_abs[feature]:.4f}')\n", "        axes[i].grid(True, alpha=0.3)\n", "        \n", "        # 添加趋势线\n", "        z = np.polyfit(df_clean[feature].dropna(), df_clean[target_col][df_clean[feature].notna()], 1)\n", "        p = np.poly1d(z)\n", "        axes[i].plot(df_clean[feature], p(df_clean[feature]), \"r--\", alpha=0.8)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 构建多种机器学习模型"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 准备数据 - 使用所有特征\n", "X = df_clean[feature_cols]\n", "y = df_clean[target_col]\n", "\n", "# 数据标准化\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "X_scaled_df = pd.DataFrame(X_scaled, columns=feature_cols, index=X.index)\n", "\n", "# 分割数据\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    X_scaled_df, y, test_size=0.2, random_state=42\n", ")\n", "\n", "print(f\"训练集大小: {X_train.shape}\")\n", "print(f\"测试集大小: {X_test.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 定义和训练多种模型\n", "models = {\n", "    '线性回归': LinearRegression(),\n", "    'Ridge回归': Ridge(alpha=1.0),\n", "    'Lasso回归': <PERSON><PERSON>(alpha=0.1),\n", "    'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5),\n", "    '随机森林': RandomForestRegressor(n_estimators=100, random_state=42),\n", "    '梯度提升': GradientBoostingRegressor(n_estimators=100, random_state=42)\n", "}\n", "\n", "# 训练和评估模型\n", "results = {}\n", "\n", "for name, model in models.items():\n", "    print(f\"\\n训练 {name}...\")\n", "    \n", "    # 训练模型\n", "    model.fit(X_train, y_train)\n", "    \n", "    # 预测\n", "    y_pred_train = model.predict(X_train)\n", "    y_pred_test = model.predict(X_test)\n", "    \n", "    # 计算指标\n", "    train_r2 = r2_score(y_train, y_pred_train)\n", "    test_r2 = r2_score(y_test, y_pred_test)\n", "    test_mse = mean_squared_error(y_test, y_pred_test)\n", "    test_mae = mean_absolute_error(y_test, y_pred_test)\n", "    \n", "    # 交叉验证\n", "    cv_scores = cross_val_score(model, X_scaled_df, y, cv=5, scoring='r2')\n", "    \n", "    results[name] = {\n", "        'model': model,\n", "        'train_r2': train_r2,\n", "        'test_r2': test_r2,\n", "        'test_mse': test_mse,\n", "        'test_mae': test_mae,\n", "        'cv_mean': cv_scores.mean(),\n", "        'cv_std': cv_scores.std(),\n", "        'predictions': y_pred_test,\n", "        'y_test': y_test\n", "    }\n", "    \n", "    print(f\"  训练R²: {train_r2:.4f}\")\n", "    print(f\"  测试R²: {test_r2:.4f}\")\n", "    print(f\"  测试MSE: {test_mse:.4f}\")\n", "    print(f\"  测试MAE: {test_mae:.4f}\")\n", "    print(f\"  交叉验证R²: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 模型性能比较"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 比较模型性能\n", "print(\"=\"*80)\n", "print(\"模型性能比较:\")\n", "print(\"=\"*80)\n", "print(f\"{'模型名称':<12} {'训练R²':<8} {'测试R²':<8} {'测试MSE':<10} {'测试MAE':<10} {'CV R²':<12} {'CV 标准差':<10}\")\n", "print(\"-\"*80)\n", "\n", "best_model_name = None\n", "best_cv_score = -np.inf\n", "\n", "for name, result in results.items():\n", "    print(f\"{name:<12} {result['train_r2']:<8.4f} {result['test_r2']:<8.4f} \"\n", "          f\"{result['test_mse']:<10.4f} {result['test_mae']:<10.4f} \"\n", "          f\"{result['cv_mean']:<12.4f} {result['cv_std']:<10.4f}\")\n", "    \n", "    if result['cv_mean'] > best_cv_score:\n", "        best_cv_score = result['cv_mean']\n", "        best_model_name = name\n", "\n", "print(\"-\"*80)\n", "print(f\"最佳模型 (基于交叉验证): {best_model_name} (CV R² = {best_cv_score:.4f})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制模型性能对比图\n", "model_names = list(results.keys())\n", "train_r2 = [results[name]['train_r2'] for name in model_names]\n", "test_r2 = [results[name]['test_r2'] for name in model_names]\n", "cv_mean = [results[name]['cv_mean'] for name in model_names]\n", "cv_std = [results[name]['cv_std'] for name in model_names]\n", "\n", "# 创建图形\n", "fig, axes = plt.subplots(2, 2, figsize=(16, 12))\n", "\n", "# 1. R²对比\n", "x = np.arange(len(model_names))\n", "width = 0.35\n", "\n", "axes[0, 0].bar(x - width/2, train_r2, width, label='训练R²', alpha=0.8)\n", "axes[0, 0].bar(x + width/2, test_r2, width, label='测试R²', alpha=0.8)\n", "axes[0, 0].set_xlabel('模型')\n", "axes[0, 0].set_ylabel('R² 分数')\n", "axes[0, 0].set_title('模型R²性能对比')\n", "axes[0, 0].set_xticks(x)\n", "axes[0, 0].set_xticklabels(model_names, rotation=45)\n", "axes[0, 0].legend()\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 2. 交叉验证结果\n", "axes[0, 1].errorbar(x, cv_mean, yerr=cv_std, fmt='o', capsize=5, capthick=2)\n", "axes[0, 1].set_xlabel('模型')\n", "axes[0, 1].set_ylabel('交叉验证 R²')\n", "axes[0, 1].set_title('交叉验证性能 (均值 ± 标准差)')\n", "axes[0, 1].set_xticks(x)\n", "axes[0, 1].set_xticklabels(model_names, rotation=45)\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. 预测vs实际 (最佳模型)\n", "best_result = results[best_model_name]\n", "axes[1, 0].scatter(best_result['y_test'], best_result['predictions'], alpha=0.6)\n", "min_val = min(best_result['y_test'].min(), best_result['predictions'].min())\n", "max_val = max(best_result['y_test'].max(), best_result['predictions'].max())\n", "axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)\n", "axes[1, 0].set_xlabel('实际值')\n", "axes[1, 0].set_ylabel('预测值')\n", "axes[1, 0].set_title(f'{best_model_name} - 预测vs实际\\nR² = {best_result[\"test_r2\"]:.4f}')\n", "axes[1, 0].grid(True, alpha=0.3)\n", "\n", "# 4. 残差图\n", "residuals = best_result['y_test'] - best_result['predictions']\n", "axes[1, 1].scatter(best_result['predictions'], residuals, alpha=0.6)\n", "axes[1, 1].axhline(y=0, color='r', linestyle='--')\n", "axes[1, 1].set_xlabel('预测值')\n", "axes[1, 1].set_ylabel('残差')\n", "axes[1, 1].set_title(f'{best_model_name} - 残差分析')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 特征重要性分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析最佳模型的特征重要性\n", "best_model = results[best_model_name]['model']\n", "\n", "# 获取特征重要性\n", "if hasattr(best_model, 'feature_importances_'):\n", "    # 树模型\n", "    importances = best_model.feature_importances_\n", "    importance_type = \"特征重要性\"\n", "<PERSON><PERSON>(best_model, 'coef_'):\n", "    # 线性模型\n", "    importances = np.abs(best_model.coef_)\n", "    importance_type = \"系数绝对值\"\n", "else:\n", "    print(\"该模型不支持特征重要性分析\")\n", "    importances = None\n", "\n", "if importances is not None:\n", "    # 创建特征重要性DataFrame\n", "    feature_importance_df = pd.DataFrame({\n", "        'feature': feature_cols,\n", "        'importance': importances\n", "    }).sort_values('importance', ascending=False)\n", "    \n", "    print(f\"\\n{importance_type}排序:\")\n", "    print(\"=\"*50)\n", "    display(feature_importance_df.head(10))\n", "    \n", "    # 绘制特征重要性图\n", "    plt.figure(figsize=(12, 8))\n", "    top_features = feature_importance_df.head(10)\n", "    \n", "    bars = plt.barh(range(len(top_features)), top_features['importance'])\n", "    plt.yticks(range(len(top_features)), top_features['feature'])\n", "    plt.xlabel(importance_type)\n", "    plt.title(f'{best_model_name} - 前10个重要特征')\n", "    plt.gca().invert_yaxis()\n", "    \n", "    # 添加数值标签\n", "    for i, bar in enumerate(bars):\n", "        width = bar.get_width()\n", "        plt.text(width, bar.get_y() + bar.get_height()/2, \n", "                f'{width:.4f}', ha='left', va='center')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 超参数调优"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 对表现最好的几个模型进行超参数调优\n", "print(\"正在进行超参数调优...\")\n", "\n", "# 定义参数网格\n", "param_grids = {\n", "    'RandomForest': {\n", "        'model': RandomForestRegressor(random_state=42),\n", "        'params': {\n", "            'n_estimators': [50, 100, 200],\n", "            'max_depth': [10, 20, None],\n", "            'min_samples_split': [2, 5, 10]\n", "        }\n", "    },\n", "    'GradientBoosting': {\n", "        'model': GradientBoostingRegressor(random_state=42),\n", "        'params': {\n", "            'n_estimators': [50, 100, 200],\n", "            'learning_rate': [0.01, 0.1, 0.2],\n", "            'max_depth': [3, 5, 7]\n", "        }\n", "    }\n", "}\n", "\n", "tuned_results = {}\n", "\n", "for name, config in param_grids.items():\n", "    print(f\"\\n调优 {name}...\")\n", "    \n", "    grid_search = GridSearchCV(\n", "        config['model'], \n", "        config['params'], \n", "        cv=5, \n", "        scoring='r2',\n", "        n_jobs=-1\n", "    )\n", "    \n", "    grid_search.fit(X_train, y_train)\n", "    \n", "    # 评估最佳模型\n", "    best_model = grid_search.best_estimator_\n", "    y_pred = best_model.predict(X_test)\n", "    test_r2 = r2_score(y_test, y_pred)\n", "    \n", "    tuned_results[name] = {\n", "        'model': best_model,\n", "        'best_params': grid_search.best_params_,\n", "        'best_score': grid_search.best_score_,\n", "        'test_r2': test_r2\n", "    }\n", "    \n", "    print(f\"  最佳参数: {grid_search.best_params_}\")\n", "    print(f\"  交叉验证分数: {grid_search.best_score_:.4f}\")\n", "    print(f\"  测试R²: {test_r2:.4f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 最终分析报告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 生成最终报告\n", "print(\"=\"*80)\n", "print(\"最终分析报告\")\n", "print(\"=\"*80)\n", "\n", "best_result = results[best_model_name]\n", "\n", "print(f\"数据集信息:\")\n", "print(f\"  - 总样本数: {len(df_clean)}\")\n", "print(f\"  - 特征数量: {len(feature_cols)}\")\n", "print(f\"  - 目标变量范围: {df_clean[target_col].min():.4f} ~ {df_clean[target_col].max():.4f}\")\n", "print(f\"  - 目标变量均值: {df_clean[target_col].mean():.4f}\")\n", "print(f\"  - 目标变量标准差: {df_clean[target_col].std():.4f}\")\n", "\n", "print(f\"\\n最佳模型: {best_model_name}\")\n", "print(f\"  - 训练R²: {best_result['train_r2']:.4f}\")\n", "print(f\"  - 测试R²: {best_result['test_r2']:.4f}\")\n", "print(f\"  - 测试MSE: {best_result['test_mse']:.4f}\")\n", "print(f\"  - 测试MAE: {best_result['test_mae']:.4f}\")\n", "print(f\"  - 交叉验证R²: {best_result['cv_mean']:.4f} ± {best_result['cv_std']:.4f}\")\n", "\n", "if 'feature_importance_df' in locals():\n", "    print(f\"\\n前5个重要特征:\")\n", "    for idx, row in feature_importance_df.head(5).iterrows():\n", "        print(f\"  {idx+1}. {row['feature']}: {row['importance']:.6f}\")\n", "\n", "print(f\"\\n模型解释:\")\n", "if best_result['test_r2'] > 0.8:\n", "    print(\"  - 模型表现优秀，能够很好地解释腐蚀电流的变化\")\n", "elif best_result['test_r2'] > 0.6:\n", "    print(\"  - 模型表现良好，能够较好地预测腐蚀电流\")\n", "elif best_result['test_r2'] > 0.4:\n", "    print(\"  - 模型表现一般，有一定的预测能力\")\n", "else:\n", "    print(\"  - 模型表现较差，需要进一步优化\")\n", "\n", "print(f\"\\n建议:\")\n", "print(\"  - 重点关注前5个重要特征的监控和控制\")\n", "print(\"  - 可以使用该模型进行腐蚀电流的预测\")\n", "print(\"  - 建议收集更多数据以进一步提升模型性能\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}