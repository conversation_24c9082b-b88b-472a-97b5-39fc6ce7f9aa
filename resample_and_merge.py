import os
import pandas as pd
import time
from datetime import datetime


def read_csv_file(file_path):
    """
    读取CSV文件

    参数:
    file_path: CSV文件路径
    """
    print(f"正在读取文件: {file_path}")
    start_time = time.time()

    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)

        end_time = time.time()
        print(f"读取完成，耗时: {end_time - start_time:.2f} 秒")

        # 显示基本信息
        print(f"文件形状: {df.shape}")
        print(f"列名: {df.columns.tolist()}")
        print(f"\n前5行数据:")
        print(df.head())

        return df

    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None


def resample_to_hourly(df, time_column):
    """
    将数据按小时重采样

    参数:
    df: 数据框
    time_column: 时间列的名称
    """
    print(f"正在将数据重采样至小时...")
    start_time = time.time()

    try:
        # 创建数据框的副本
        df = df.copy()

        # 检查DataFrame是否为空
        if df.empty:
            print("警告: 数据框为空，无法进行重采样")
            return None

        # 检查时间列是否存在
        if time_column not in df.columns:
            print(f"错误: 时间列 '{time_column}' 不存在于数据框中")
            print(f"可用的列: {df.columns.tolist()}")
            return None

        # 显示时间列的前几个值，帮助调试
        print(f"时间列 '{time_column}' 的前5个值:")
        print(df[time_column].head())

        # 尝试自动检测时间格式并转换
        try:
            # 不指定格式，让pandas自动检测
            df[time_column] = pd.to_datetime(df[time_column], errors="coerce")
        except Exception as e:
            print(f"自动检测时间格式失败: {e}")
            # 如果自动检测失败，尝试常见的格式
            formats = ["%Y/%m/%d %H:%M", "%Y-%m-%d %H:%M", "%Y-%m-%d %H:%M:%S"]
            for fmt in formats:
                try:
                    print(f"尝试时间格式: {fmt}")
                    df[time_column] = pd.to_datetime(
                        df[time_column], format=fmt, errors="coerce"
                    )
                    break
                except:
                    continue

        # 检查是否有无效的时间值
        invalid_dates = df[time_column].isna().sum()
        if invalid_dates > 0:
            print(f"警告: 发现 {invalid_dates} 个无效的时间值，这些行将被删除")
            df = df.dropna(subset=[time_column])

        # 如果处理后DataFrame为空，则返回
        if df.empty:
            print("警告: 处理时间列后数据框为空，无法进行重采样")
            return None

        # 设置时间列为索引
        df.set_index(time_column, inplace=True)

        # 获取数值列
        numeric_columns = df.select_dtypes(include=["float64", "int64"]).columns

        if len(numeric_columns) == 0:
            print("警告: 没有找到数值列，无法进行重采样")
            return None

        # 按小时重采样，只对数值列进行平均
        hourly_df = df[numeric_columns].resample("h").mean()

        # 重置索引，将时间列恢复为普通列
        hourly_df.reset_index(inplace=True)

        end_time = time.time()
        print(f"重采样完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"重采样后的数据形状: {hourly_df.shape}")
        print("\n重采样后的前5行数据:")
        print(hourly_df.head())

        return hourly_df

    except Exception as e:
        print(f"重采样时出错: {e}")
        # 安全地尝试打印时间列
        try:
            if time_column in df.columns:
                print(f"\n时间列的前5行:\n{df[time_column].head()}")
            else:
                print(f"时间列 '{time_column}' 不存在于数据框中")
        except:
            print("无法打印时间列信息")
        return None


def merge_datasets(df1, df2, time_column):
    """
    合并两个数据集

    参数:
    df1: 第一个数据框
    df2: 第二个数据框
    time_column: 时间列的名称
    """
    print("正在合并数据集...")
    start_time = time.time()

    try:
        # 确保两个数据框的时间列是datetime类型
        df1[time_column] = pd.to_datetime(df1[time_column])
        df2[time_column] = pd.to_datetime(df2[time_column])

        # 删除无效的时间值
        df1 = df1.dropna(subset=[time_column])
        df2 = df2.dropna(subset=[time_column])

        # 基于时间列合并两个数据框
        merged_df = pd.merge(
            df1, df2, on=time_column, how="outer", suffixes=("_环境", "_碳钢")
        )

        # 按时间排序
        merged_df.sort_values(by=time_column, inplace=True)

        end_time = time.time()
        print(f"合并完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"合并后的数据形状: {merged_df.shape}")
        print("\n合并后的前5行数据:")
        print(merged_df.head())

        return merged_df

    except Exception as e:
        print(f"合并数据集时出错: {e}")
        return None


def save_to_csv(df, output_path):
    """
    将数据框保存为CSV格式

    参数:
    df: 数据框
    output_path: 输出文件路径
    """
    print(f"正在保存数据到: {output_path}")
    start_time = time.time()

    try:
        # 保存为CSV格式
        df.to_csv(output_path, index=False, encoding="utf-8")

        end_time = time.time()
        print(f"保存完成，耗时: {end_time - start_time:.2f} 秒")
        print(f"文件已保存到: {output_path}")

    except Exception as e:
        print(f"保存数据时出错: {e}")


def main():
    # 文件路径
    file1_path = "file/广东湛江(1).csv"
    file2_path = "file/碳钢1年.csv"

    # 检查文件是否存在
    if not os.path.exists(file1_path):
        print(f"错误: 文件 {file1_path} 不存在")
        return
    if not os.path.exists(file2_path):
        print(f"错误: 文件 {file2_path} 不存在")
        return

    # 输出文件路径
    output_path = "file/merged_hourly_data.csv"

    # 确保输出目录存在
    os.makedirs(os.path.dirname(output_path), exist_ok=True)

    # 读取第一个文件
    df1 = read_csv_file(file1_path)
    if df1 is None:
        return

    # 读取第二个文件
    df2 = read_csv_file(file2_path)
    if df2 is None:
        return

    # 检查并修正时间列格式问题
    print("\n检查并修正时间列格式...")

    # 获取时间列名称
    time_column = None
    possible_time_columns = ["time_points", "时间", "datetime", "date", "timestamp"]

    # 尝试找到时间列
    for col in possible_time_columns:
        if col in df1.columns and col in df2.columns:
            time_column = col
            break

    if time_column is None:
        print("错误: 未找到匹配的时间列")
        print(f"文件1的列: {df1.columns.tolist()}")
        print(f"文件2的列: {df2.columns.tolist()}")
        return

    print(f"使用时间列: {time_column}")

    # 重采样第一个文件
    hourly_df1 = resample_to_hourly(df1, time_column)
    if hourly_df1 is None:
        return

    # 重采样第二个文件
    hourly_df2 = resample_to_hourly(df2, time_column)
    if hourly_df2 is None:
        return

    # 合并数据集
    merged_df = merge_datasets(hourly_df1, hourly_df2, time_column)
    if merged_df is None:
        return

    # 保存为CSV格式
    save_to_csv(merged_df, output_path)

    print("\n处理完成！")
    print(f"合并后的数据已保存到: {output_path}")


if __name__ == "__main__":
    main()
