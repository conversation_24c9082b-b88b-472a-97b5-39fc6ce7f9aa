#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版腐蚀电流影响分析
使用所有环境特征分析对腐蚀电流(i1)的影响，包含缺失值处理和模型优化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, KFold
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet, LogisticRegression
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor, RandomForestClassifier
from sklearn.preprocessing import PolynomialFeatures, StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.impute import SimpleImputer, KNNImputer
from scipy import stats
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def load_and_analyze_data():
    """加载数据并进行详细分析"""
    print("正在加载和分析数据...")

    # 读取数据
    df = pd.read_csv('file/merged_hourly_data.csv')

    print("="*60)
    print("数据基本信息:")
    print("="*60)
    print(f"数据形状: {df.shape}")
    print(f"时间范围: {df['time_points'].min()} 到 {df['time_points'].max()}")

    # 定义特征列（排除时间列）
    feature_cols = [col for col in df.columns if col not in ['time_points', 'i1']]
    target_col = 'i1'

    print(f"\n环境特征列 ({len(feature_cols)}个):")
    for i, col in enumerate(feature_cols, 1):
        print(f"{i:2d}. {col}")

    print(f"\n目标变量: {target_col}")

    # 缺失值分析
    print("\n" + "="*60)
    print("缺失值分析:")
    print("="*60)
    missing_stats = df.isnull().sum()
    missing_pct = (missing_stats / len(df)) * 100

    missing_df = pd.DataFrame({
        '缺失数量': missing_stats,
        '缺失百分比': missing_pct
    }).sort_values('缺失数量', ascending=False)

    print(missing_df[missing_df['缺失数量'] > 0])

    # 数据分布分析
    print("\n" + "="*60)
    print("数据统计摘要:")
    print("="*60)
    print(df[feature_cols + [target_col]].describe())

    return df, feature_cols, target_col

def handle_missing_values(df, feature_cols, target_col, method='knn'):
    """处理缺失值"""
    print(f"\n正在使用 {method} 方法处理缺失值...")

    # 首先删除目标变量为空的行
    df_clean = df.dropna(subset=[target_col]).copy()
    print(f"删除目标变量缺失行后，数据形状: {df_clean.shape}")

    # 处理特征列的缺失值
    if method == 'mean':
        imputer = SimpleImputer(strategy='mean')
    elif method == 'median':
        imputer = SimpleImputer(strategy='median')
    elif method == 'knn':
        imputer = KNNImputer(n_neighbors=5)
    else:
        # 删除所有缺失值
        df_clean = df_clean.dropna()
        print(f"删除所有缺失值后，数据形状: {df_clean.shape}")
        return df_clean, feature_cols

    # 应用插补
    X_features = df_clean[feature_cols]
    X_imputed = imputer.fit_transform(X_features)

    # 创建新的DataFrame
    df_imputed = df_clean.copy()
    df_imputed[feature_cols] = X_imputed

    print(f"插补后的数据形状: {df_imputed.shape}")
    print("插补后缺失值检查:")
    print(df_imputed[feature_cols + [target_col]].isnull().sum().sum())

    return df_imputed, feature_cols

def feature_correlation_analysis(df, feature_cols, target_col):
    """特征相关性分析"""
    print("\n正在进行特征相关性分析...")

    # 计算与目标变量的相关性
    correlations = df[feature_cols + [target_col]].corr()[target_col].drop(target_col)
    correlations_abs = correlations.abs().sort_values(ascending=False)

    print("\n与腐蚀电流(i1)的相关性排序:")
    print("="*50)
    for feature, corr in correlations_abs.items():
        print(f"{feature:<15}: {correlations[feature]:>8.4f} (|{corr:.4f}|)")

    # 绘制相关性热力图
    plt.figure(figsize=(12, 10))
    correlation_matrix = df[feature_cols + [target_col]].corr()

    # 创建掩码，只显示上三角
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))

    sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.3f')
    plt.title('特征相关性矩阵')
    plt.tight_layout()
    plt.savefig('file/photo/enhanced_correlation_matrix.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 选择高相关性特征
    high_corr_features = correlations_abs[correlations_abs > 0.1].index.tolist()
    print(f"\n高相关性特征 (|相关系数| > 0.1): {len(high_corr_features)}个")
    print(high_corr_features)

    return correlations, high_corr_features

def visualize_feature_relationships(df, feature_cols, target_col, top_n=6):
    """可视化特征与目标变量的关系"""
    print(f"\n正在绘制前{top_n}个重要特征的关系图...")

    # 计算相关性并选择前N个特征
    correlations = df[feature_cols].corrwith(df[target_col]).abs().sort_values(ascending=False)
    top_features = correlations.head(top_n).index.tolist()

    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.ravel()

    for i, feature in enumerate(top_features):
        if i < len(axes):
            axes[i].scatter(df[feature], df[target_col], alpha=0.6, s=20)
            axes[i].set_xlabel(feature)
            axes[i].set_ylabel('腐蚀电流 i1')
            axes[i].set_title(f'{feature} vs 腐蚀电流\n相关系数: {correlations[feature]:.4f}')
            axes[i].grid(True, alpha=0.3)

            # 添加趋势线
            z = np.polyfit(df[feature].dropna(), df[target_col][df[feature].notna()], 1)
            p = np.poly1d(z)
            axes[i].plot(df[feature], p(df[feature]), "r--", alpha=0.8)

    plt.tight_layout()
    plt.savefig('file/photo/feature_relationships.png', dpi=300, bbox_inches='tight')
    plt.show()

    return top_features

def build_enhanced_models(df, feature_cols, target_col, use_all_features=True):
    """构建多种增强模型"""
    print("\n正在构建增强模型...")

    # 准备数据
    if use_all_features:
        X = df[feature_cols]
        feature_names = feature_cols
    else:
        # 只使用高相关性特征
        correlations = df[feature_cols].corrwith(df[target_col]).abs()
        high_corr_features = correlations[correlations > 0.1].index.tolist()
        X = df[high_corr_features]
        feature_names = high_corr_features
        print(f"使用高相关性特征: {len(feature_names)}个")

    y = df[target_col]

    # 数据标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    X_scaled_df = pd.DataFrame(X_scaled, columns=feature_names, index=X.index)

    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled_df, y, test_size=0.2, random_state=42
    )

    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")

    # 定义模型
    models = {
        '线性回归': LinearRegression(),
        'Ridge回归': Ridge(alpha=1.0),
        'Lasso回归': Lasso(alpha=0.1),
        'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5),
        '随机森林': RandomForestRegressor(n_estimators=100, random_state=42),
        '梯度提升': GradientBoostingRegressor(n_estimators=100, random_state=42)
    }

    # 训练和评估模型
    results = {}

    for name, model in models.items():
        print(f"\n训练 {name}...")

        # 训练模型
        model.fit(X_train, y_train)

        # 预测
        y_pred_train = model.predict(X_train)
        y_pred_test = model.predict(X_test)

        # 计算指标
        train_r2 = r2_score(y_train, y_pred_train)
        test_r2 = r2_score(y_test, y_pred_test)
        test_mse = mean_squared_error(y_test, y_pred_test)
        test_mae = mean_absolute_error(y_test, y_pred_test)

        # 交叉验证
        cv_scores = cross_val_score(model, X_scaled_df, y, cv=5, scoring='r2')

        results[name] = {
            'model': model,
            'train_r2': train_r2,
            'test_r2': test_r2,
            'test_mse': test_mse,
            'test_mae': test_mae,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'predictions': y_pred_test,
            'y_test': y_test
        }

        print(f"  训练R²: {train_r2:.4f}")
        print(f"  测试R²: {test_r2:.4f}")
        print(f"  测试MSE: {test_mse:.4f}")
        print(f"  测试MAE: {test_mae:.4f}")
        print(f"  交叉验证R²: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

    return results, X_train, X_test, y_train, y_test, scaler, feature_names

def compare_model_performance(results):
    """比较模型性能"""
    print("\n" + "="*80)
    print("模型性能比较:")
    print("="*80)
    print(f"{'模型名称':<12} {'训练R²':<8} {'测试R²':<8} {'测试MSE':<10} {'测试MAE':<10} {'CV R²':<12} {'CV 标准差':<10}")
    print("-"*80)

    best_model_name = None
    best_cv_score = -np.inf

    for name, result in results.items():
        print(f"{name:<12} {result['train_r2']:<8.4f} {result['test_r2']:<8.4f} "
              f"{result['test_mse']:<10.4f} {result['test_mae']:<10.4f} "
              f"{result['cv_mean']:<12.4f} {result['cv_std']:<10.4f}")

        if result['cv_mean'] > best_cv_score:
            best_cv_score = result['cv_mean']
            best_model_name = name

    print("-"*80)
    print(f"最佳模型 (基于交叉验证): {best_model_name} (CV R² = {best_cv_score:.4f})")

    return best_model_name

def plot_model_comparison(results):
    """绘制模型性能对比图"""
    print("\n正在生成模型对比图...")

    # 准备数据
    model_names = list(results.keys())
    train_r2 = [results[name]['train_r2'] for name in model_names]
    test_r2 = [results[name]['test_r2'] for name in model_names]
    cv_mean = [results[name]['cv_mean'] for name in model_names]
    cv_std = [results[name]['cv_std'] for name in model_names]

    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))

    # 1. R²对比
    x = np.arange(len(model_names))
    width = 0.35

    axes[0, 0].bar(x - width/2, train_r2, width, label='训练R²', alpha=0.8)
    axes[0, 0].bar(x + width/2, test_r2, width, label='测试R²', alpha=0.8)
    axes[0, 0].set_xlabel('模型')
    axes[0, 0].set_ylabel('R² 分数')
    axes[0, 0].set_title('模型R²性能对比')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(model_names, rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)

    # 2. 交叉验证结果
    axes[0, 1].errorbar(x, cv_mean, yerr=cv_std, fmt='o', capsize=5, capthick=2)
    axes[0, 1].set_xlabel('模型')
    axes[0, 1].set_ylabel('交叉验证 R²')
    axes[0, 1].set_title('交叉验证性能 (均值 ± 标准差)')
    axes[0, 1].set_xticks(x)
    axes[0, 1].set_xticklabels(model_names, rotation=45)
    axes[0, 1].grid(True, alpha=0.3)

    # 3. 预测vs实际 (选择最佳模型)
    best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
    best_result = results[best_model_name]

    axes[1, 0].scatter(best_result['y_test'], best_result['predictions'], alpha=0.6)
    min_val = min(best_result['y_test'].min(), best_result['predictions'].min())
    max_val = max(best_result['y_test'].max(), best_result['predictions'].max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)
    axes[1, 0].set_xlabel('实际值')
    axes[1, 0].set_ylabel('预测值')
    axes[1, 0].set_title(f'{best_model_name} - 预测vs实际\nR² = {best_result["test_r2"]:.4f}')
    axes[1, 0].grid(True, alpha=0.3)

    # 4. 残差图
    residuals = best_result['y_test'] - best_result['predictions']
    axes[1, 1].scatter(best_result['predictions'], residuals, alpha=0.6)
    axes[1, 1].axhline(y=0, color='r', linestyle='--')
    axes[1, 1].set_xlabel('预测值')
    axes[1, 1].set_ylabel('残差')
    axes[1, 1].set_title(f'{best_model_name} - 残差分析')
    axes[1, 1].grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('file/photo/enhanced_model_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

def analyze_feature_importance(results, feature_names, best_model_name):
    """分析特征重要性"""
    print(f"\n正在分析 {best_model_name} 的特征重要性...")

    best_model = results[best_model_name]['model']

    # 获取特征重要性
    if hasattr(best_model, 'feature_importances_'):
        # 树模型
        importances = best_model.feature_importances_
        importance_type = "特征重要性"
    elif hasattr(best_model, 'coef_'):
        # 线性模型
        importances = np.abs(best_model.coef_)
        importance_type = "系数绝对值"
    else:
        print("该模型不支持特征重要性分析")
        return

    # 创建特征重要性DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': feature_names,
        'importance': importances
    }).sort_values('importance', ascending=False)

    print(f"\n{importance_type}排序:")
    print("="*50)
    for idx, row in feature_importance_df.head(10).iterrows():
        print(f"{row['feature']:<15}: {row['importance']:.6f}")

    # 绘制特征重要性图
    plt.figure(figsize=(12, 8))
    top_features = feature_importance_df.head(10)

    bars = plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel(importance_type)
    plt.title(f'{best_model_name} - 前10个重要特征')
    plt.gca().invert_yaxis()

    # 添加数值标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width, bar.get_y() + bar.get_height()/2,
                f'{width:.4f}', ha='left', va='center')

    plt.tight_layout()
    plt.savefig('file/photo/enhanced_feature_importance.png', dpi=300, bbox_inches='tight')
    plt.show()

    return feature_importance_df

def hyperparameter_tuning(df_clean, feature_cols, target_col):
    """超参数调优"""
    print("\n正在进行超参数调优...")

    # 准备数据
    X = df_clean[feature_cols]
    y = df_clean[target_col]

    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42
    )

    # 定义参数网格
    param_grids = {
        'RandomForest': {
            'model': RandomForestRegressor(random_state=42),
            'params': {
                'n_estimators': [50, 100, 200],
                'max_depth': [10, 20, None],
                'min_samples_split': [2, 5, 10]
            }
        },
        'GradientBoosting': {
            'model': GradientBoostingRegressor(random_state=42),
            'params': {
                'n_estimators': [50, 100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            }
        }
    }

    best_models = {}

    for name, config in param_grids.items():
        print(f"\n调优 {name}...")

        grid_search = GridSearchCV(
            config['model'],
            config['params'],
            cv=5,
            scoring='r2',
            n_jobs=-1
        )

        grid_search.fit(X_train, y_train)

        # 评估最佳模型
        best_model = grid_search.best_estimator_
        y_pred = best_model.predict(X_test)
        test_r2 = r2_score(y_test, y_pred)

        best_models[name] = {
            'model': best_model,
            'best_params': grid_search.best_params_,
            'best_score': grid_search.best_score_,
            'test_r2': test_r2,
            'predictions': y_pred,
            'y_test': y_test
        }

        print(f"  最佳参数: {grid_search.best_params_}")
        print(f"  交叉验证分数: {grid_search.best_score_:.4f}")
        print(f"  测试R²: {test_r2:.4f}")

    return best_models

def double_machine_learning_analysis(df_clean, feature_cols, target_col, treatment_feature):
    """
    双重机器学习分析 - 估计特定环境因素对腐蚀电流的因果效应

    参数:
    df_clean: 清理后的数据
    feature_cols: 所有特征列
    target_col: 目标变量（腐蚀电流）
    treatment_feature: 处理变量（我们想分析因果效应的特征，如温度）
    """
    print(f"\n" + "="*80)
    print(f"双重机器学习分析: {treatment_feature} 对 {target_col} 的因果效应")
    print("="*80)

    # 准备数据
    # 控制变量：除了处理变量外的所有其他特征
    control_features = [col for col in feature_cols if col != treatment_feature]

    X_control = df_clean[control_features]  # 控制变量
    T = df_clean[treatment_feature]         # 处理变量
    Y = df_clean[target_col]               # 结果变量

    print(f"控制变量数量: {len(control_features)}")
    print(f"处理变量: {treatment_feature}")
    print(f"结果变量: {target_col}")
    print(f"样本数量: {len(df_clean)}")

    # 数据标准化
    scaler_control = StandardScaler()
    X_control_scaled = scaler_control.fit_transform(X_control)

    scaler_treatment = StandardScaler()
    T_scaled = scaler_treatment.fit_transform(T.values.reshape(-1, 1)).flatten()

    # 使用交叉拟合避免过拟合
    n_folds = 5
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

    # 存储残差
    Y_residuals = np.zeros(len(Y))
    T_residuals = np.zeros(len(T))

    print(f"\n第一步：使用{n_folds}折交叉拟合计算残差...")

    for fold, (train_idx, test_idx) in enumerate(kf.split(X_control_scaled)):
        print(f"  处理第 {fold+1}/{n_folds} 折...")

        # 训练集和测试集
        X_train, X_test = X_control_scaled[train_idx], X_control_scaled[test_idx]
        T_train, T_test = T_scaled[train_idx], T_scaled[test_idx]
        Y_train, Y_test = Y.iloc[train_idx], Y.iloc[test_idx]

        # 模型1：预测结果变量Y（基于控制变量，不包含处理变量）
        # 这相当于预测"如果没有处理变量影响，Y应该是多少"
        model_Y = RandomForestRegressor(n_estimators=100, random_state=42)
        model_Y.fit(X_train, Y_train)
        Y_pred = model_Y.predict(X_test)

        # 计算Y的残差：实际Y - 预测Y（去除控制变量影响后的"纯净"Y）
        Y_residuals[test_idx] = Y_test - Y_pred

        # 模型2：预测处理变量T（基于控制变量）
        # 这相当于预测"基于控制变量，T应该是多少"
        model_T = RandomForestRegressor(n_estimators=100, random_state=42)
        model_T.fit(X_train, T_train)
        T_pred = model_T.predict(X_test)

        # 计算T的残差：实际T - 预测T（去除控制变量影响后的"纯净"T）
        T_residuals[test_idx] = T_test - T_pred

    print("第一步完成：已计算出去除混淆变量影响后的残差")

    # 第二步：分析残差之间的关系
    print(f"\n第二步：分析残差关系...")

    # 使用线性回归分析残差关系
    # Y_residual = α + β * T_residual + ε
    # β 就是我们要的因果效应估计
    causal_model = LinearRegression()
    causal_model.fit(T_residuals.reshape(-1, 1), Y_residuals)

    causal_effect = causal_model.coef_[0]
    intercept = causal_model.intercept_
    r2_causal = causal_model.score(T_residuals.reshape(-1, 1), Y_residuals)

    # 计算置信区间和显著性
    correlation, p_value = pearsonr(T_residuals, Y_residuals)

    # 计算标准误差
    n = len(T_residuals)
    mse = np.mean((Y_residuals - causal_model.predict(T_residuals.reshape(-1, 1)))**2)
    var_T = np.var(T_residuals)
    se_beta = np.sqrt(mse / (var_T * (n - 1)))

    # 95%置信区间
    t_critical = stats.t.ppf(0.975, n - 2)
    ci_lower = causal_effect - t_critical * se_beta
    ci_upper = causal_effect + t_critical * se_beta

    print(f"\n双重机器学习结果:")
    print("="*50)
    print(f"因果效应估计 (β): {causal_effect:.6f}")
    print(f"标准误差: {se_beta:.6f}")
    print(f"95%置信区间: [{ci_lower:.6f}, {ci_upper:.6f}]")
    print(f"R²: {r2_causal:.4f}")
    print(f"相关系数: {correlation:.4f}")
    print(f"p值: {p_value:.6f}")

    # 解释结果
    print(f"\n结果解释:")
    if p_value < 0.001:
        significance = "极显著 (p < 0.001)"
    elif p_value < 0.01:
        significance = "高度显著 (p < 0.01)"
    elif p_value < 0.05:
        significance = "显著 (p < 0.05)"
    else:
        significance = "不显著 (p >= 0.05)"

    print(f"- 统计显著性: {significance}")

    if abs(causal_effect) > 0.01:
        effect_size = "大"
    elif abs(causal_effect) > 0.001:
        effect_size = "中等"
    else:
        effect_size = "小"

    print(f"- 效应大小: {effect_size}")

    # 实际意义解释
    treatment_std = df_clean[treatment_feature].std()
    target_std = df_clean[target_col].std()

    # 标准化效应：处理变量增加1个标准差，结果变量变化多少个标准差
    standardized_effect = causal_effect * treatment_std / target_std

    print(f"- 标准化效应: {standardized_effect:.4f}")
    print(f"- 实际意义: {treatment_feature}增加1个标准差({treatment_std:.4f})时，")
    print(f"  {target_col}平均变化{causal_effect * treatment_std:.6f}单位")

    # 可视化残差关系
    plt.figure(figsize=(12, 8))

    # 主图：残差散点图
    plt.subplot(2, 2, 1)
    plt.scatter(T_residuals, Y_residuals, alpha=0.6, s=20)

    # 添加拟合线
    T_range = np.linspace(T_residuals.min(), T_residuals.max(), 100)
    Y_fit = causal_effect * T_range + intercept
    plt.plot(T_range, Y_fit, 'r-', linewidth=2,
             label=f'因果效应: {causal_effect:.6f}')

    plt.xlabel(f'{treatment_feature} 残差（去除混淆变量影响）')
    plt.ylabel(f'{target_col} 残差（去除混淆变量影响）')
    plt.title(f'双重机器学习：{treatment_feature} → {target_col}\n因果效应 = {causal_effect:.6f} (p = {p_value:.4f})')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 残差分布
    plt.subplot(2, 2, 2)
    plt.hist(T_residuals, bins=30, alpha=0.7, color='blue', edgecolor='black')
    plt.xlabel(f'{treatment_feature} 残差')
    plt.ylabel('频次')
    plt.title(f'{treatment_feature} 残差分布')
    plt.grid(True, alpha=0.3)

    plt.subplot(2, 2, 3)
    plt.hist(Y_residuals, bins=30, alpha=0.7, color='green', edgecolor='black')
    plt.xlabel(f'{target_col} 残差')
    plt.ylabel('频次')
    plt.title(f'{target_col} 残差分布')
    plt.grid(True, alpha=0.3)

    # QQ图检验残差正态性
    plt.subplot(2, 2, 4)
    stats.probplot(Y_residuals, dist="norm", plot=plt)
    plt.title('Y残差正态性检验')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'file/photo/dml_analysis_{treatment_feature}.png', dpi=300, bbox_inches='tight')
    plt.show()

    return {
        'treatment_feature': treatment_feature,
        'causal_effect': causal_effect,
        'standard_error': se_beta,
        'confidence_interval': (ci_lower, ci_upper),
        'r2': r2_causal,
        'p_value': p_value,
        'correlation': correlation,
        'standardized_effect': standardized_effect,
        'T_residuals': T_residuals,
        'Y_residuals': Y_residuals,
        'significance': significance,
        'effect_size': effect_size
    }

def comprehensive_causal_analysis(df_clean, feature_cols, target_col, top_n_features=5):
    """
    对多个重要特征进行双重机器学习分析
    """
    print(f"\n" + "="*80)
    print("综合因果分析：分析多个环境因素的因果效应")
    print("="*80)

    # 选择最相关的特征进行因果分析
    correlations = df_clean[feature_cols].corrwith(df_clean[target_col]).abs()
    top_features = correlations.nlargest(top_n_features).index.tolist()

    print(f"选择前{top_n_features}个相关性最高的特征进行因果分析:")
    for i, feature in enumerate(top_features, 1):
        print(f"{i}. {feature}: 相关系数 = {correlations[feature]:.4f}")

    # 对每个特征进行DML分析
    causal_results = {}

    for feature in top_features:
        print(f"\n{'='*60}")
        print(f"分析 {feature} 的因果效应...")
        print('='*60)

        result = double_machine_learning_analysis(df_clean, feature_cols, target_col, feature)
        causal_results[feature] = result

    # 汇总结果
    print(f"\n" + "="*100)
    print("因果效应汇总表")
    print("="*100)
    print(f"{'特征':<15} {'因果效应':<12} {'标准误差':<10} {'95%置信区间':<25} {'p值':<10} {'显著性':<15}")
    print("-"*100)

    for feature, result in causal_results.items():
        ci_str = f"[{result['confidence_interval'][0]:.4f}, {result['confidence_interval'][1]:.4f}]"
        print(f"{feature:<15} {result['causal_effect']:<12.6f} {result['standard_error']:<10.6f} "
              f"{ci_str:<25} {result['p_value']:<10.6f} {result['significance']:<15}")

    # 绘制因果效应对比图
    plt.figure(figsize=(14, 8))

    features = list(causal_results.keys())
    effects = [causal_results[f]['causal_effect'] for f in features]
    errors = [causal_results[f]['standard_error'] for f in features]
    colors = ['red' if causal_results[f]['p_value'] < 0.05 else 'gray' for f in features]

    bars = plt.bar(range(len(features)), effects, yerr=errors, capsize=5,
                   color=colors, alpha=0.7, edgecolor='black')

    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.xlabel('环境特征')
    plt.ylabel('因果效应估计')
    plt.title('各环境因素对腐蚀电流的因果效应\n(红色=显著, 灰色=不显著)')
    plt.xticks(range(len(features)), features, rotation=45)
    plt.grid(True, alpha=0.3)

    # 添加数值标签
    for i, (bar, effect, error) in enumerate(zip(bars, effects, errors)):
        plt.text(bar.get_x() + bar.get_width()/2,
                bar.get_height() + error + 0.001 if effect > 0 else bar.get_height() - error - 0.001,
                f'{effect:.4f}', ha='center', va='bottom' if effect > 0 else 'top')

    plt.tight_layout()
    plt.savefig('file/photo/comprehensive_causal_effects.png', dpi=300, bbox_inches='tight')
    plt.show()

    return causal_results

def generate_final_report(results, best_model_name, feature_importance_df, df_clean, target_col):
    """生成最终报告"""
    print("\n" + "="*80)
    print("最终分析报告")
    print("="*80)

    best_result = results[best_model_name]

    print(f"数据集信息:")
    print(f"  - 总样本数: {len(df_clean)}")
    print(f"  - 特征数量: {len(feature_importance_df)}")
    print(f"  - 目标变量范围: {df_clean[target_col].min():.4f} ~ {df_clean[target_col].max():.4f}")
    print(f"  - 目标变量均值: {df_clean[target_col].mean():.4f}")
    print(f"  - 目标变量标准差: {df_clean[target_col].std():.4f}")

    print(f"\n最佳模型: {best_model_name}")
    print(f"  - 训练R²: {best_result['train_r2']:.4f}")
    print(f"  - 测试R²: {best_result['test_r2']:.4f}")
    print(f"  - 测试MSE: {best_result['test_mse']:.4f}")
    print(f"  - 测试MAE: {best_result['test_mae']:.4f}")
    print(f"  - 交叉验证R²: {best_result['cv_mean']:.4f} ± {best_result['cv_std']:.4f}")

    print(f"\n前5个重要特征:")
    for idx, row in feature_importance_df.head(5).iterrows():
        print(f"  {idx+1}. {row['feature']}: {row['importance']:.6f}")

    print(f"\n模型解释:")
    if best_result['test_r2'] > 0.8:
        print("  - 模型表现优秀，能够很好地解释腐蚀电流的变化")
    elif best_result['test_r2'] > 0.6:
        print("  - 模型表现良好，能够较好地预测腐蚀电流")
    elif best_result['test_r2'] > 0.4:
        print("  - 模型表现一般，有一定的预测能力")
    else:
        print("  - 模型表现较差，需要进一步优化")

    print(f"\n建议:")
    print("  - 重点关注前5个重要特征的监控和控制")
    print("  - 可以使用该模型进行腐蚀电流的预测")
    print("  - 建议收集更多数据以进一步提升模型性能")

if __name__ == "__main__":
    print("开始增强版腐蚀电流影响分析...")

    # 1. 数据加载和分析
    df, feature_cols, target_col = load_and_analyze_data()

    # 2. 处理缺失值
    df_clean, feature_cols = handle_missing_values(df, feature_cols, target_col, method='knn')

    # 3. 特征相关性分析
    correlations, high_corr_features = feature_correlation_analysis(df_clean, feature_cols, target_col)

    # 4. 可视化特征关系
    top_features = visualize_feature_relationships(df_clean, feature_cols, target_col)

    # 5. 构建增强模型
    results, X_train, X_test, y_train, y_test, scaler, feature_names = build_enhanced_models(
        df_clean, feature_cols, target_col, use_all_features=True
    )

    # 6. 模型性能比较
    best_model_name = compare_model_performance(results)

    # 7. 绘制模型对比图
    plot_model_comparison(results)

    # 8. 特征重要性分析
    feature_importance_df = analyze_feature_importance(results, feature_names, best_model_name)

    # 9. 超参数调优
    tuned_models = hyperparameter_tuning(df_clean, feature_cols, target_col)

    # 10. 双重机器学习因果分析
    print("\n" + "="*80)
    print("开始双重机器学习因果分析...")
    print("="*80)

    # 对前5个重要特征进行因果分析
    causal_results = comprehensive_causal_analysis(df_clean, feature_cols, target_col, top_n_features=5)

    # 11. 生成增强的最终报告（包含因果分析）
    generate_final_report(results, best_model_name, feature_importance_df, df_clean, target_col)

    print("\n分析完成！")
    print("生成的文件:")
    print("- file/photo/enhanced_correlation_matrix.png: 增强相关性矩阵")
    print("- file/photo/feature_relationships.png: 特征关系图")
    print("- file/photo/enhanced_model_comparison.png: 增强模型对比图")
    print("- file/photo/enhanced_feature_importance.png: 增强特征重要性图")
    print("- file/photo/comprehensive_causal_effects.png: 综合因果效应图")
    print("- file/photo/dml_analysis_*.png: 各特征的双重机器学习分析图")


