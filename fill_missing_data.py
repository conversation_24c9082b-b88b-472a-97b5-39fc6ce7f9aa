import pandas as pd
import numpy as np
from datetime import datetime
import os

# 打印当前工作目录和文件是否存在
print("当前工作目录:", os.getcwd())
print("process_data2.csv 是否存在:", os.path.exists("process_data2.csv"))
print("file/process_data2.csv 是否存在:", os.path.exists("file/process_data2.csv"))

# 读取两个数据文件
print("正在读取数据文件...")
df_final = pd.read_csv("file/merged_final_data.csv")
df_hourly = pd.read_csv("file/process_data2.csv")

print("原始数据形状:", df_final.shape)
print("补充数据形状:", df_hourly.shape)

# 打印两个数据集的列名，以便调试
print("\n原始数据列名:", df_final.columns.tolist())
print("补充数据列名:", df_hourly.columns.tolist())

# 确保时间列是datetime类型并规范化时间格式
print("正在处理时间格式...")
# 尝试多种可能的日期格式
try:
    df_final["datetime"] = pd.to_datetime(df_final["datetime"])
except:
    try:
        df_final["datetime"] = pd.to_datetime(
            df_final["datetime"], format="%Y/%m/%d %H:%M"
        )
    except:
        print("警告：无法解析原始数据时间格式")

try:
    df_hourly["time_points"] = pd.to_datetime(df_hourly["time_points"])
except:
    try:
        df_hourly["time_points"] = pd.to_datetime(
            df_hourly["time_points"], format="%Y-%m-%d %H:%M:%S"
        )
    except:
        print("警告：无法解析补充数据时间格式")

# 打印两个数据集的时间范围和几个示例，以便调试
print("\n原始数据时间范围：")
print("开始时间：", df_final["datetime"].min())
print("结束时间：", df_final["datetime"].max())
print("原始数据时间示例（前5个）:")
print(df_final["datetime"].head())

print("\n补充数据时间范围：")
print("开始时间：", df_hourly["time_points"].min())
print("结束时间：", df_hourly["time_points"].max())
print("补充数据时间示例（前5个）:")
print(df_hourly["time_points"].head())

# 需要填补的列
columns_to_fill = [
    "temp",
    "humi",
    "airpress",
    "condensation",
    "H2S",
    "SO2",
    "PM1_0",
    "PM2_5",
    "PM10",
    "NO2",
    "CO2",
    "O3",
    "illuminance",
]

# 分析两个数据集的时间点重叠情况
df_final_times = set(df_final["datetime"].dt.floor("h"))
df_hourly_times = set(df_hourly["time_points"].dt.floor("h"))

common_times = df_final_times.intersection(df_hourly_times)
only_in_final = df_final_times - df_hourly_times
only_in_hourly = df_hourly_times - df_final_times

print(f"\n两个数据集时间点重叠情况:")
print(f"原始数据中的时间点数量: {len(df_final_times)}")
print(f"补充数据中的时间点数量: {len(df_hourly_times)}")
print(f"两个数据集共有的时间点数量: {len(common_times)}")
print(f"只在原始数据中出现的时间点数量: {len(only_in_final)}")
print(f"只在补充数据中出现的时间点数量: {len(only_in_hourly)}")

# 检查有缺失值的时间点
missing_times = df_final[df_final[columns_to_fill].isna().any(axis=1)][
    "datetime"
].dt.floor("h")
missing_times_set = set(missing_times)
missing_times_in_hourly = missing_times_set.intersection(df_hourly_times)

print(f"\n有缺失值的时间点数量: {len(missing_times_set)}")
print(f"有缺失值且在补充数据中存在的时间点数量: {len(missing_times_in_hourly)}")

print("\n有缺失值的时间点示例（前10个）:")
for i, ts in enumerate(sorted(list(missing_times_set))[:10]):
    print(f"  {i + 1}. {ts}")

print("\n有缺失值且在补充数据中存在的时间点示例（前10个）:")
for i, ts in enumerate(sorted(list(missing_times_in_hourly))[:10]):
    print(f"  {i + 1}. {ts}")

# 显示数据示例
print("\n原始数据前3行:")
print(df_final.head(3))
print("\n补充数据前3行:")
print(df_hourly.head(3))

# 检查数据中是否存在缺失值
missing_in_original = df_final.isna().sum().sum()
print(f"\n原始数据中的总缺失值数量: {missing_in_original}")

# 检查这些列在两个数据集中是否都存在
print("\n检查列名是否匹配:")
for col in columns_to_fill:
    in_final = col in df_final.columns
    in_hourly = col in df_hourly.columns
    print(
        f"列 {col}: 原始数据中{'' if in_final else '不'}存在, 补充数据中{'' if in_hourly else '不'}存在"
    )

# 检查每列的缺失值数量
print("\n原始数据中各列的缺失值数量:")
for col in columns_to_fill:
    if col in df_final.columns:
        missing_count = df_final[col].isna().sum()
        print(f"{col}: {missing_count} ({missing_count / len(df_final) * 100:.2f}%)")

# 找到第一个有缺失值的行
first_missing_row = None
first_missing_col = None
for idx, row in df_final.iterrows():
    has_missing = False
    for col in columns_to_fill:
        if pd.isna(row[col]):
            first_missing_row = row
            first_missing_col = col
            has_missing = True
            break
    if has_missing:
        break

if first_missing_row is not None:
    print("\n第一个缺失值的行:")
    print(first_missing_row)
    print(f"缺失值所在列: {first_missing_col}")
    print(f"缺失值所在行的时间: {first_missing_row['datetime']}")

    # 检查补充数据中是否有这个时间点
    missing_time = pd.Timestamp(first_missing_row["datetime"]).floor("h")
    matching_hourly = df_hourly[df_hourly["time_points"].dt.floor("h") == missing_time]
    print(
        f"\n补充数据中是否有匹配的时间点: {'是' if not matching_hourly.empty else '否'}"
    )
    if not matching_hourly.empty:
        print("匹配的补充数据行:")
        print(matching_hourly)
        print(
            f"补充数据中 {first_missing_col} 列的值: {matching_hourly.iloc[0][first_missing_col]}"
        )

# 记录填补和跳过的数据
filled_times = []
skipped_times = []

print("\n开始数据填补过程...")
# 根据datetime创建一个时间到行索引的映射
hourly_dict = {}
for _, row in df_hourly.iterrows():
    # 使用时间戳作为索引（精确到小时）
    timestamp = pd.Timestamp(row["time_points"]).floor("h")
    hourly_dict[timestamp] = row

print(f"\n补充数据字典包含 {len(hourly_dict)} 个时间点")
print("补充数据字典的前5个键:")
for i, key in enumerate(list(hourly_dict.keys())[:5]):
    print(f"  {i + 1}. {key}")

if first_missing_row is not None:
    missing_timestamp = pd.Timestamp(first_missing_row["datetime"]).floor("h")
    print(
        f"\n第一个缺失值的时间戳 {missing_timestamp} 是否在补充数据字典中: {'是' if missing_timestamp in hourly_dict else '否'}"
    )
    if missing_timestamp in hourly_dict:
        print(
            f"补充数据中对应的 {first_missing_col} 值: {hourly_dict[missing_timestamp][first_missing_col]}"
        )

filled_count = 0
debug_count = 0
# 遍历原始数据中的每一行
for index, row in df_final.iterrows():
    # 将原始数据中的时间点取整到小时
    try:
        timestamp = pd.Timestamp(row["datetime"]).floor("h")

        # 检查是否有缺失值
        missing_columns = [
            col
            for col in columns_to_fill
            if col in df_final.columns and pd.isna(row[col])
        ]

        if missing_columns and timestamp in hourly_dict:
            # 获取补充数据
            supplement_row = hourly_dict[timestamp]

            # 填补缺失值
            for col in missing_columns:
                if col in supplement_row.index and not pd.isna(supplement_row[col]):
                    # 打印前10个填补操作的详细信息
                    if debug_count < 10:
                        print(
                            f"填补: 行 {index}, 时间 {row['datetime']}, 列 {col}, 值 {supplement_row[col]}"
                        )
                        debug_count += 1

                    df_final.at[index, col] = supplement_row[col]
                    filled_times.append((row["datetime"], col))
                    filled_count += 1
                else:
                    skipped_times.append((row["datetime"], col))
        elif missing_columns:
            # 如果没有找到对应的补充数据，记录为跳过
            for col in missing_columns:
                skipped_times.append((row["datetime"], col))
    except Exception as e:
        print(f"处理行 {index} 时出错: {e}")

print(f"数据填补完成，成功填补了 {filled_count} 个数据点，正在保存结果...")
# 保存填补后的数据
df_final.to_csv("file/merged_final_data_filled.csv", index=False)

# 输出填补和跳过的数据统计
print("\n填补的数据统计：")
print(f"总共填补了 {len(filled_times)} 个数据点")
if filled_times:
    print("\n填补的具体时间点和列（显示前10个）：")
    for time_point, col in filled_times[:10]:
        print(f"时间: {time_point}, 列: {col}")
    if len(filled_times) > 10:
        print(f"... 还有 {len(filled_times) - 10} 个数据点")

print("\n跳过的数据统计：")
print(f"总共跳过了 {len(skipped_times)} 个数据点")
if skipped_times:
    print("\n跳过的具体时间点和列（显示前10个）：")
    for time_point, col in skipped_times[:10]:
        print(f"时间: {time_point}, 列: {col}")
    if len(skipped_times) > 10:
        print(f"... 还有 {len(skipped_times) - 10} 个数据点")

# 输出原始和填补后的缺失值统计
print("\n原始数据缺失值统计：")
original_missing = df_final[columns_to_fill].isna().sum()
print(original_missing)

print("\n填补后数据缺失值统计：")
filled_missing = (
    pd.read_csv("file/merged_final_data_filled.csv")[columns_to_fill].isna().sum()
)
print(filled_missing)

# 计算填补率
total_missing = sum(original_missing)
total_filled = total_missing - sum(filled_missing)
filled_rate = (total_filled / total_missing) * 100 if total_missing > 0 else 0
print(f"\n总填补率: {filled_rate:.2f}%")

print("\n填补后的数据分布统计：")
print(pd.read_csv("file/merged_final_data_filled.csv")[columns_to_fill].describe())
