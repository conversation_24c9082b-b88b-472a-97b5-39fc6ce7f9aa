import pandas as pd
import numpy as np
from datetime import datetime

# 读取环境数据CSV文件
df = pd.read_csv("file/广东湛江(1).csv")

# 将时间戳转换为datetime格式
df["time_points"] = pd.to_datetime(df["time_points"])

# 创建小时时间戳列
df["hour"] = df["time_points"].dt.floor("h")

# 需要计算平均值的列
mean_columns = [
    "temp",
    "humi",
    "airpress",
    "condensation",
    "H2S",
    "SO2",
    "PM1_0",
    "PM2_5",
    "PM10",
    "NO2",
    "CO2",
    "O3",
    "illuminance",
]

# 将数值列转换为float类型
for col in mean_columns:
    df[col] = pd.to_numeric(df[col], errors="coerce")

# 按小时分组并计算平均值
hourly_data = df.groupby("hour")[mean_columns].mean().reset_index()

# 重命名hour列为time_points
hourly_data = hourly_data.rename(columns={"hour": "time_points"})

# 添加id列
hourly_data["id"] = range(len(hourly_data))

# 重新排列列的顺序
columns_order = ["time_points", "id"] + mean_columns
hourly_data = hourly_data[columns_order]

# 保存重采样后的数据
hourly_data.to_csv("file/process_data2.csv", index=False)

print("数据重采样完成，已保存到 file/process_data2.csv")
print(f"原始数据形状: {df.shape}")
print(f"重采样后数据形状: {hourly_data.shape}")

# 打印数据统计信息
print("\n重采样后数据统计：")
print(hourly_data[mean_columns].describe())
