"""
此脚本用于合并两个CSV文件：
1. merged_hourly_data.csv：包含主要的传感器数据
2. transformed_环境数据3303A.csv：包含环境监测站3303A的数据

合并策略：
- 使用datetime作为合并键
- 以merged_hourly_data.csv的时间点为基准进行左连接
- 为避免列名冲突，给环境数据3303A的列添加后缀'_3303A'

输出文件：
- 生成merged_final_data.csv，包含所有merged_hourly_data的数据和对应时间点的环境数据
- 保持merged_hourly_data的所有时间点，如果某时间点在环境数据中没有对应值，则填充为NaN

作者：[作者名]
日期：[日期]
"""

import pandas as pd

# 读取两个CSV文件
df1 = pd.read_csv("file/merged_hourly_data.csv")
df2 = pd.read_csv("file/merged_vertical_data.csv")

# 将time_points列转换为datetime格式
df1["datetime"] = pd.to_datetime(df1["time_points"])

# 确保两个数据框的datetime列格式一致
df2["datetime"] = pd.to_datetime(df2["datetime"])

# 重命名df2中的重复列名
df2 = df2.add_suffix("_3303A")
df2["datetime"] = pd.to_datetime(df2["datetime_3303A"])
df2 = df2.drop("datetime_3303A", axis=1)

# 使用merge函数合并数据
# 使用left join确保只保留merged_hourly_data中的时间点
merged_df = pd.merge(df1, df2, on="datetime", how="left")

# 删除重复的列（如果有的话）
# 保留原始merged_hourly_data中的列名
columns_to_keep = df1.columns.tolist() + [
    col for col in df2.columns if col not in df1.columns
]
merged_df = merged_df[columns_to_keep]

# 保存合并后的数据
merged_df.to_csv("file/merged_final_data.csv", index=False)

# 打印一些基本信息
print(f"原始merged_hourly_data行数: {len(df1)}")
print(f"原始环境数据行数: {len(df2)}")
print(f"合并后的数据行数: {len(merged_df)}")
print("\n合并后的数据列名:")
print(merged_df.columns.tolist())
