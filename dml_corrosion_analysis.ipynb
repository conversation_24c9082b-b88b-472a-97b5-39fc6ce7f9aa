{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 双重机器学习腐蚀分析\n", "\n", "使用双重机器学习(Double Machine Learning)方法分析环境因素对腐蚀电流的**因果效应**"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入所有必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV, KFold\n", "from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error\n", "from sklearn.impute import KNNImputer\n", "from scipy import stats\n", "from scipy.stats import pearsonr\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"双重机器学习分析环境准备完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载和预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取数据\n", "df = pd.read_csv('file/merged_hourly_data.csv')\n", "\n", "print(\"数据基本信息:\")\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"时间范围: {df['time_points'].min()} 到 {df['time_points'].max()}\")\n", "\n", "# 定义特征列和目标列\n", "feature_cols = [col for col in df.columns if col not in ['time_points', 'i1']]\n", "target_col = 'i1'\n", "\n", "print(f\"\\n环境特征列 ({len(feature_cols)}个): {feature_cols}\")\n", "print(f\"目标变量: {target_col}\")\n", "\n", "# 检查缺失值\n", "print(\"\\n缺失值统计:\")\n", "missing_stats = df.isnull().sum()\n", "display(missing_stats[missing_stats > 0])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 处理缺失值\n", "print(\"正在处理缺失值...\")\n", "\n", "# 删除目标变量缺失的行\n", "df_clean = df.dropna(subset=[target_col]).copy()\n", "print(f\"删除目标变量缺失行后，数据形状: {df_clean.shape}\")\n", "\n", "# 使用KNN插补处理特征缺失值\n", "imputer = KNNImputer(n_neighbors=5)\n", "X_features = df_clean[feature_cols]\n", "X_imputed = imputer.fit_transform(X_features)\n", "\n", "# 更新数据\n", "df_clean[feature_cols] = X_imputed\n", "print(f\"插补后缺失值检查: {df_clean[feature_cols + [target_col]].isnull().sum().sum()}\")\n", "print(f\"最终数据形状: {df_clean.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 双重机器学习原理说明\n", "\n", "**传统分析的问题：**\n", "- 简单的相关性分析无法区分因果关系\n", "- 混淆变量会影响结果的准确性\n", "\n", "**双重机器学习的解决方案：**\n", "1. **第一步**：用机器学习模型\"过滤\"掉混淆变量的影响\n", "   - 模型1：预测腐蚀电流（基于除目标环境因素外的所有其他因素）\n", "   - 模型2：预测目标环境因素（基于所有其他因素）\n", "   - 计算残差：实际值 - 预测值\n", "\n", "2. **第二步**：分析两个残差的关系\n", "   - 残差代表\"去除混淆变量影响后的纯净效应\"\n", "   - 残差间的关系就是真正的因果效应"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 选择分析目标"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算各特征与目标变量的相关性，选择最重要的几个进行因果分析\n", "correlations = df_clean[feature_cols].corrwith(df_clean[target_col]).abs().sort_values(ascending=False)\n", "\n", "print(\"各环境因素与腐蚀电流的相关性:\")\n", "print(\"=\"*50)\n", "for feature, corr in correlations.items():\n", "    print(f\"{feature:<15}: {corr:.4f}\")\n", "\n", "# 选择前5个最相关的特征进行因果分析\n", "top_features = correlations.head(5).index.tolist()\n", "print(f\"\\n选择进行因果分析的特征: {top_features}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 双重机器学习分析函数"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def double_machine_learning_analysis(df_clean, feature_cols, target_col, treatment_feature):\n", "    \"\"\"\n", "    双重机器学习分析特定环境因素对腐蚀电流的因果效应\n", "    \"\"\"\n", "    print(f\"\\n分析 {treatment_feature} 对 {target_col} 的因果效应\")\n", "    print(\"=\"*60)\n", "    \n", "    # 准备数据\n", "    control_features = [col for col in feature_cols if col != treatment_feature]\n", "    X_control = df_clean[control_features]  # 控制变量\n", "    T = df_clean[treatment_feature]         # 处理变量\n", "    Y = df_clean[target_col]               # 结果变量\n", "    \n", "    print(f\"控制变量数量: {len(control_features)}\")\n", "    print(f\"样本数量: {len(df_clean)}\")\n", "    \n", "    # 数据标准化\n", "    scaler_control = StandardScaler()\n", "    X_control_scaled = scaler_control.fit_transform(X_control)\n", "    \n", "    scaler_treatment = StandardScaler()\n", "    T_scaled = scaler_treatment.fit_transform(T.values.reshape(-1, 1)).flatten()\n", "    \n", "    # 交叉拟合避免过拟合\n", "    n_folds = 5\n", "    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)\n", "    \n", "    Y_residuals = np.zeros(len(Y))\n", "    T_residuals = np.zeros(len(T))\n", "    \n", "    print(f\"\\n第一步：使用{n_folds}折交叉拟合计算残差...\")\n", "    \n", "    for fold, (train_idx, test_idx) in enumerate(kf.split(X_control_scaled)):\n", "        # 训练集和测试集\n", "        X_train, X_test = X_control_scaled[train_idx], X_control_scaled[test_idx]\n", "        T_train, T_test = T_scaled[train_idx], T_scaled[test_idx]\n", "        Y_train, Y_test = Y.iloc[train_idx], Y.iloc[test_idx]\n", "        \n", "        # 模型1：预测Y（基于控制变量）\n", "        model_Y = RandomForestRegressor(n_estimators=100, random_state=42)\n", "        model_Y.fit(X_train, Y_train)\n", "        Y_pred = model_Y.predict(X_test)\n", "        Y_residuals[test_idx] = Y_test - Y_pred\n", "        \n", "        # 模型2：预测T（基于控制变量）\n", "        model_T = RandomForestRegressor(n_estimators=100, random_state=42)\n", "        model_T.fit(X_train, T_train)\n", "        T_pred = model_T.predict(X_test)\n", "        T_residuals[test_idx] = T_test - T_pred\n", "    \n", "    print(\"第一步完成：已计算残差\")\n", "    \n", "    # 第二步：分析残差关系\n", "    print(\"\\n第二步：分析残差关系...\")\n", "    \n", "    # 线性回归分析残差关系\n", "    causal_model = LinearRegression()\n", "    causal_model.fit(T_residuals.reshape(-1, 1), Y_residuals)\n", "    \n", "    causal_effect = causal_model.coef_[0]\n", "    intercept = causal_model.intercept_\n", "    r2_causal = causal_model.score(T_residuals.reshape(-1, 1), Y_residuals)\n", "    \n", "    # 统计检验\n", "    correlation, p_value = pearsonr(T_residuals, Y_residuals)\n", "    \n", "    # 计算置信区间\n", "    n = len(T_residuals)\n", "    mse = np.mean((Y_residuals - causal_model.predict(T_residuals.reshape(-1, 1)))**2)\n", "    var_T = np.var(T_residuals)\n", "    se_beta = np.sqrt(mse / (var_T * (n - 1)))\n", "    \n", "    t_critical = stats.t.ppf(0.975, n - 2)\n", "    ci_lower = causal_effect - t_critical * se_beta\n", "    ci_upper = causal_effect + t_critical * se_beta\n", "    \n", "    # 结果输出\n", "    print(f\"\\n双重机器学习结果:\")\n", "    print(f\"因果效应估计: {causal_effect:.6f}\")\n", "    print(f\"95%置信区间: [{ci_lower:.6f}, {ci_upper:.6f}]\")\n", "    print(f\"R²: {r2_causal:.4f}\")\n", "    print(f\"p值: {p_value:.6f}\")\n", "    \n", "    # 显著性判断\n", "    if p_value < 0.001:\n", "        significance = \"极显著 (p < 0.001)\"\n", "    elif p_value < 0.01:\n", "        significance = \"高度显著 (p < 0.01)\"\n", "    elif p_value < 0.05:\n", "        significance = \"显著 (p < 0.05)\"\n", "    else:\n", "        significance = \"不显著 (p >= 0.05)\"\n", "    \n", "    print(f\"统计显著性: {significance}\")\n", "    \n", "    # 实际意义\n", "    treatment_std = df_clean[treatment_feature].std()\n", "    print(f\"实际意义: {treatment_feature}增加1个标准差({treatment_std:.4f})时，\")\n", "    print(f\"腐蚀电流平均变化{causal_effect * treatment_std:.6f}单位\")\n", "    \n", "    return {\n", "        'treatment_feature': treatment_feature,\n", "        'causal_effect': causal_effect,\n", "        'confidence_interval': (ci_lower, ci_upper),\n", "        'r2': r2_causal,\n", "        'p_value': p_value,\n", "        'significance': significance,\n", "        'T_residuals': T_residuals,\n", "        'Y_residuals': Y_residuals\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 对重要特征进行因果分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 对每个重要特征进行双重机器学习分析\n", "causal_results = {}\n", "\n", "for feature in top_features:\n", "    result = double_machine_learning_analysis(df_clean, feature_cols, target_col, feature)\n", "    causal_results[feature] = result"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化残差关系"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 为每个特征绘制残差关系图\n", "n_features = len(causal_results)\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "axes = axes.ravel()\n", "\n", "for i, (feature, result) in enumerate(causal_results.items()):\n", "    if i < len(axes):\n", "        T_residuals = result['T_residuals']\n", "        Y_residuals = result['Y_residuals']\n", "        causal_effect = result['causal_effect']\n", "        p_value = result['p_value']\n", "        \n", "        # 散点图\n", "        axes[i].scatter(T_residuals, Y_residuals, alpha=0.6, s=20)\n", "        \n", "        # 拟合线\n", "        T_range = np.linspace(T_residuals.min(), T_residuals.max(), 100)\n", "        Y_fit = causal_effect * T_range\n", "        axes[i].plot(T_range, Y_fit, 'r-', linewidth=2)\n", "        \n", "        axes[i].set_xlabel(f'{feature} 残差')\n", "        axes[i].set_ylabel('腐蚀电流 残差')\n", "        axes[i].set_title(f'{feature}\\n因果效应: {causal_effect:.6f}\\np值: {p_value:.4f}')\n", "        axes[i].grid(True, alpha=0.3)\n", "\n", "# 隐藏多余的子图\n", "for i in range(n_features, len(axes)):\n", "    axes[i].set_visible(False)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 因果效应汇总分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建因果效应汇总表\n", "summary_data = []\n", "for feature, result in causal_results.items():\n", "    summary_data.append({\n", "        '环境因素': feature,\n", "        '因果效应': result['causal_effect'],\n", "        '置信区间下限': result['confidence_interval'][0],\n", "        '置信区间上限': result['confidence_interval'][1],\n", "        'R²': result['r2'],\n", "        'p值': result['p_value'],\n", "        '显著性': result['significance']\n", "    })\n", "\n", "summary_df = pd.DataFrame(summary_data)\n", "summary_df = summary_df.sort_values('因果效应', key=abs, ascending=False)\n", "\n", "print(\"因果效应汇总表:\")\n", "print(\"=\"*80)\n", "display(summary_df)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制因果效应对比图\n", "plt.figure(figsize=(12, 8))\n", "\n", "features = summary_df['环境因素'].tolist()\n", "effects = summary_df['因果效应'].tolist()\n", "p_values = summary_df['p值'].tolist()\n", "\n", "# 根据显著性设置颜色\n", "colors = ['red' if p < 0.05 else 'gray' for p in p_values]\n", "\n", "bars = plt.bar(range(len(features)), effects, color=colors, alpha=0.7, edgecolor='black')\n", "\n", "plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "plt.xlabel('环境因素')\n", "plt.ylabel('因果效应估计')\n", "plt.title('各环境因素对腐蚀电流的因果效应\\n(红色=显著, 灰色=不显著)')\n", "plt.xticks(range(len(features)), features, rotation=45)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 添加数值标签\n", "for i, (bar, effect) in enumerate(zip(bars, effects)):\n", "    plt.text(bar.get_x() + bar.get_width()/2, \n", "            bar.get_height() + 0.001 if effect > 0 else bar.get_height() - 0.001,\n", "            f'{effect:.4f}', ha='center', va='bottom' if effect > 0 else 'top')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 结果解释和应用建议"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析结果并给出建议\n", "print(\"=\"*80)\n", "print(\"双重机器学习分析结果总结\")\n", "print(\"=\"*80)\n", "\n", "# 找出显著的因果因素\n", "significant_factors = summary_df[summary_df['p值'] < 0.05]\n", "\n", "print(f\"\\n1. 统计显著的因果因素 (p < 0.05):\")\n", "if len(significant_factors) > 0:\n", "    for _, row in significant_factors.iterrows():\n", "        effect = row['因果效应']\n", "        feature = row['环境因素']\n", "        direction = \"增加\" if effect > 0 else \"减少\"\n", "        print(f\"  - {feature}: {direction}腐蚀电流 (效应: {effect:.6f})\")\nelse:\n", "    print(\"  - 未发现统计显著的因果关系\")\n", "\n", "print(f\"\\n2. 效应大小排序:\")\n", "for i, (_, row) in enumerate(summary_df.iterrows(), 1):\n", "    print(f\"  {i}. {row['环境因素']}: {row['因果效应']:.6f} ({row['显著性']})\")\n", "\n", "print(f\"\\n3. 实际应用建议:\")\n", "print(f\"  预测方面:\")\n", "print(f\"    - 传统机器学习模型仍然适用于腐蚀电流预测\")\n", "print(f\"    - 重点监控相关性高的环境因素\")\n", "\n", "print(f\"  控制方面:\")\n", "if len(significant_factors) > 0:\n", "    print(f\"    - 重点控制以下因素以减少腐蚀:\")\n", "    for _, row in significant_factors.iterrows():\n", "        effect = row['因果效应']\n", "        feature = row['环境因素']\n", "        if effect > 0:\n", "            print(f\"      * 降低{feature}水平\")\n", "        else:\n", "            print(f\"      * 提高{feature}水平\")\nelse:\n", "    print(f\"    - 基于当前数据，未发现明确的可控因果因素\")\n", "    print(f\"    - 建议收集更多数据或考虑其他潜在因素\")\n", "\n", "print(f\"\\n4. 方法论优势:\")\n", "print(f\"  - 双重机器学习排除了混淆变量的干扰\")\n", "print(f\"  - 提供了比相关性分析更可靠的因果推断\")\n", "print(f\"  - 结果可直接用于指导环境控制策略\")\n", "print(f\"  - 避免了传统因果推断方法的强假设\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 传统分析 vs 因果分析对比"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 对比传统相关性分析和因果分析的结果\n", "print(\"传统相关性分析 vs 双重机器学习因果分析对比:\")\n", "print(\"=\"*70)\n", "\n", "# 计算相关性\n", "correlations_comparison = df_clean[top_features].corrwith(df_clean[target_col])\n", "\n", "comparison_data = []\n", "for feature in top_features:\n", "    corr = correlations_comparison[feature]\n", "    causal_effect = causal_results[feature]['causal_effect']\n", "    p_value = causal_results[feature]['p_value']\n", "    \n", "    comparison_data.append({\n", "        '环境因素': feature,\n", "        '相关系数': corr,\n", "        '因果效应': causal_effect,\n", "        '因果分析p值': p_value,\n", "        '差异': abs(corr) - abs(causal_effect)\n", "    })\n", "\n", "comparison_df = pd.DataFrame(comparison_data)\n", "display(comparison_df)\n", "\n", "print(\"\\n关键差异:\")\n", "print(\"- 相关系数: 衡量线性关联强度，但可能受混淆变量影响\")\n", "print(\"- 因果效应: 排除混淆变量后的真实因果关系\")\n", "print(\"- 差异较大的因素可能存在较强的混淆变量影响\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}