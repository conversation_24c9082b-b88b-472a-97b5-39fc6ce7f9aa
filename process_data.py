import pandas as pd
import numpy as np
from datetime import datetime

# 读取环境数据CSV文件
df_env = pd.read_csv("file/tyd2311-2312.csv")

# 将时间戳转换为datetime格式
df_env["time_points"] = pd.to_datetime(df_env["time_points"])

# 创建小时时间戳列
df_env["hour"] = df_env["time_points"].dt.floor("h")

# 需要计算众数的列
mode_columns = ["wind_direction"]

# 需要计算平均值的列（排除id和mode_columns）
mean_columns = [
    col
    for col in df_env.columns
    if col not in ["id", "time_points", "hour"] + mode_columns
]

# 将数值列转换为float类型
for col in mean_columns:
    df_env[col] = pd.to_numeric(df_env[col], errors="coerce")

# 按小时分组并计算统计值
hourly_env_data = pd.DataFrame()

# 计算平均值
hourly_env_data = df_env.groupby("hour")[mean_columns].mean()

# 计算众数
for col in mode_columns:
    hourly_env_data[col] = df_env.groupby("hour")[col].agg(
        lambda x: x.mode()[0] if not x.mode().empty else np.nan
    )

# 重置索引，使hour成为普通列
hourly_env_data = hourly_env_data.reset_index()

# 读取3303数据
df_3303 = pd.read_csv("file/3303A2311-2312.csv")

# 将3303数据的时间转换为datetime格式
df_3303["datetime"] = pd.to_datetime(df_3303["datetime"])

# 重命名环境数据的hour列为datetime，以便合并
hourly_env_data = hourly_env_data.rename(columns={"hour": "datetime"})

# 合并数据集
merged_data = pd.merge(df_3303, hourly_env_data, on="datetime", how="left")

# 显示合并后的数据示例
print("合并后的数据示例：")
print(merged_data.head())
print("\n数据形状：", merged_data.shape)

# 保存合并后的数据
merged_data.to_csv("file/merged_data_3303_env.csv", index=False)
print("\n数据已保存到 'file/merged_data_3303_env.csv'")
