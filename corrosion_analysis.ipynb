# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import PolynomialFeatures
from sklearn.metrics import r2_score, mean_squared_error
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("库导入完成！")

# 读取合并后的数据
df = pd.read_csv('file/merged_hourly_data.csv')

# 显示基本信息
print("数据基本信息:")
print(f"数据形状: {df.shape}")
print(f"列名: {df.columns.tolist()}")

# 检查目标变量和特征变量
target = 'i1'
features = ['temp', 'humi']

print(f"\n目标变量: {target}")
print(f"特征变量: {features}")

# 检查缺失值
print(f"\n缺失值统计:")
print(df[features + [target]].isnull().sum())

# 删除包含缺失值的行
df_clean = df[features + [target]].dropna()
print(f"\n清理后的数据形状: {df_clean.shape}")

# 显示基本统计信息
print(f"\n基本统计信息:")
display(df_clean.describe())

# 创建图形
fig, axes = plt.subplots(2, 2, figsize=(15, 12))

# 1. 温度与腐蚀电流的关系
axes[0, 0].scatter(df_clean['temp'], df_clean['i1'], alpha=0.6, color='red')
axes[0, 0].set_xlabel('温度 (°C)')
axes[0, 0].set_ylabel('腐蚀电流 i1')
axes[0, 0].set_title('温度 vs 腐蚀电流')
axes[0, 0].grid(True, alpha=0.3)

# 2. 湿度与腐蚀电流的关系
axes[0, 1].scatter(df_clean['humi'], df_clean['i1'], alpha=0.6, color='blue')
axes[0, 1].set_xlabel('湿度 (%)')
axes[0, 1].set_ylabel('腐蚀电流 i1')
axes[0, 1].set_title('湿度 vs 腐蚀电流')
axes[0, 1].grid(True, alpha=0.3)

# 3. 温度和湿度的散点图（颜色表示腐蚀电流）
scatter = axes[1, 0].scatter(df_clean['temp'], df_clean['humi'], 
                            c=df_clean['i1'], cmap='viridis', alpha=0.6)
axes[1, 0].set_xlabel('温度 (°C)')
axes[1, 0].set_ylabel('湿度 (%)')
axes[1, 0].set_title('温度-湿度关系（颜色表示腐蚀电流）')
plt.colorbar(scatter, ax=axes[1, 0])

# 4. 腐蚀电流的分布
axes[1, 1].hist(df_clean['i1'], bins=50, alpha=0.7, color='green', edgecolor='black')
axes[1, 1].set_xlabel('腐蚀电流 i1')
axes[1, 1].set_ylabel('频次')
axes[1, 1].set_title('腐蚀电流分布')
axes[1, 1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 相关性分析
correlation_matrix = df_clean[features + [target]].corr()
print(f"相关性矩阵:")
print(correlation_matrix)

# 绘制相关性热力图
plt.figure(figsize=(8, 6))
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, 
            square=True, linewidths=0.5)
plt.title('温度、湿度与腐蚀电流的相关性')
plt.show()

# 准备数据
X = df_clean[features].values
y = df_clean[target].values

# 分割训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# 存储不同模型的结果
models_results = {}

print(f"训练集大小: {X_train.shape}")
print(f"测试集大小: {X_test.shape}")

# 1. 线性模型: i1 = a*temp + b*humi + c
print("1. 线性模型分析:")
linear_model = LinearRegression()
linear_model.fit(X_train, y_train)
y_pred_linear = linear_model.predict(X_test)
r2_linear = r2_score(y_test, y_pred_linear)
mse_linear = mean_squared_error(y_test, y_pred_linear)

print(f"线性模型系数: 温度={linear_model.coef_[0]:.6f}, 湿度={linear_model.coef_[1]:.6f}")
print(f"截距: {linear_model.intercept_:.6f}")
print(f"线性模型方程: i1 = {linear_model.coef_[0]:.6f}*temp + {linear_model.coef_[1]:.6f}*humi + {linear_model.intercept_:.6f}")
print(f"R² = {r2_linear:.4f}, MSE = {mse_linear:.6f}")

models_results['线性模型'] = {'r2': r2_linear, 'mse': mse_linear, 'model': linear_model, 'predictions': y_pred_linear}

# 2. 二次多项式模型
print("\n2. 二次多项式模型分析:")
poly2_features = PolynomialFeatures(degree=2, include_bias=False)
X_train_poly2 = poly2_features.fit_transform(X_train)
X_test_poly2 = poly2_features.transform(X_test)

poly2_model = LinearRegression()
poly2_model.fit(X_train_poly2, y_train)
y_pred_poly2 = poly2_model.predict(X_test_poly2)
r2_poly2 = r2_score(y_test, y_pred_poly2)
mse_poly2 = mean_squared_error(y_test, y_pred_poly2)

print(f"二次多项式模型 R² = {r2_poly2:.4f}, MSE = {mse_poly2:.6f}")
print(f"特征名称: {poly2_features.get_feature_names_out(['temp', 'humi'])}")
print(f"系数: {poly2_model.coef_}")

models_results['二次多项式'] = {'r2': r2_poly2, 'mse': mse_poly2, 'model': poly2_model, 'predictions': y_pred_poly2}

# 3. 三次多项式模型
print("\n3. 三次多项式模型分析:")
poly3_features = PolynomialFeatures(degree=3, include_bias=False)
X_train_poly3 = poly3_features.fit_transform(X_train)
X_test_poly3 = poly3_features.transform(X_test)

poly3_model = LinearRegression()
poly3_model.fit(X_train_poly3, y_train)
y_pred_poly3 = poly3_model.predict(X_test_poly3)
r2_poly3 = r2_score(y_test, y_pred_poly3)
mse_poly3 = mean_squared_error(y_test, y_pred_poly3)

print(f"三次多项式模型 R² = {r2_poly3:.4f}, MSE = {mse_poly3:.6f}")

models_results['三次多项式'] = {'r2': r2_poly3, 'mse': mse_poly3, 'model': poly3_model, 'predictions': y_pred_poly3}

# 4. 包含交互项的模型: i1 = a*temp + b*humi + c*temp*humi + d*temp² + e*humi² + f
print("4. 交互作用模型分析:")

# 手动创建交互特征
X_interaction = np.column_stack([
    X,  # temp, humi
    X[:, 0] * X[:, 1],  # temp * humi
    X[:, 0] ** 2,  # temp²
    X[:, 1] ** 2   # humi²
])

X_train_int, X_test_int, y_train_int, y_test_int = train_test_split(
    X_interaction, y, test_size=0.2, random_state=42)

interaction_model = LinearRegression()
interaction_model.fit(X_train_int, y_train_int)
y_pred_int = interaction_model.predict(X_test_int)
r2_int = r2_score(y_test_int, y_pred_int)
mse_int = mean_squared_error(y_test_int, y_pred_int)

feature_names = ['temp', 'humi', 'temp*humi', 'temp²', 'humi²']
print(f"交互作用模型系数:")
for i, (name, coef) in enumerate(zip(feature_names, interaction_model.coef_)):
    print(f"  {name}: {coef:.6f}")
print(f"截距: {interaction_model.intercept_:.6f}")
print(f"R² = {r2_int:.4f}, MSE = {mse_int:.6f}")

models_results['交互作用模型'] = {'r2': r2_int, 'mse': mse_int, 'model': interaction_model, 'predictions': y_pred_int}

# 构建完整的交互作用方程
equation = f"i1 = {interaction_model.coef_[0]:.6f}*temp + {interaction_model.coef_[1]:.6f}*humi + {interaction_model.coef_[2]:.6f}*temp*humi + {interaction_model.coef_[3]:.6f}*temp² + {interaction_model.coef_[4]:.6f}*humi² + {interaction_model.intercept_:.6f}"
print(f"\n完整的交互作用方程:")
print(equation)

# 比较所有模型
print(f"模型性能比较:")
print("="*60)
print(f"{'模型名称':<15} {'R²':<10} {'MSE':<15}")
print("="*60)

best_model_name = None
best_r2 = -np.inf

for name, results in models_results.items():
    print(f"{name:<15} {results['r2']:<10.4f} {results['mse']:<15.6f}")
    if results['r2'] > best_r2:
        best_r2 = results['r2']
        best_model_name = name

print("="*60)
print(f"最佳模型: {best_model_name} (R² = {best_r2:.4f})")

# 绘制预测值 vs 实际值的对比图
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
axes = axes.ravel()

model_predictions = [
    ('线性模型', models_results['线性模型']['predictions']),
    ('二次多项式', models_results['二次多项式']['predictions']),
    ('三次多项式', models_results['三次多项式']['predictions']),
    ('交互作用模型', models_results['交互作用模型']['predictions'])
]

for i, (name, y_pred) in enumerate(model_predictions):
    # 使用对应的测试集
    if name == '交互作用模型':
        y_test_plot = y_test_int
    else:
        y_test_plot = y_test
        
    axes[i].scatter(y_test_plot, y_pred, alpha=0.6)
    axes[i].plot([y_test_plot.min(), y_test_plot.max()], [y_test_plot.min(), y_test_plot.max()], 'r--', lw=2)
    axes[i].set_xlabel('实际值')
    axes[i].set_ylabel('预测值')
    axes[i].set_title(f'{name}\nR² = {models_results[name]["r2"]:.4f}')
    axes[i].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# 分析温度和湿度的相对重要性
print(f"影响因子分析:")
print("="*50)

# 使用最佳模型进行分析
if best_model_name == '交互作用模型':
    coefficients = interaction_model.coef_
    feature_names = ['temp', 'humi', 'temp*humi', 'temp²', 'humi²']
    
    # 计算各项的相对重要性（基于系数的绝对值）
    abs_coefficients = np.abs(coefficients)
    relative_importance = abs_coefficients / np.sum(abs_coefficients) * 100
    
    print("各项的相对重要性:")
    for name, coef, importance in zip(feature_names, coefficients, relative_importance):
        print(f"{name:<12}: 系数={coef:>10.6f}, 重要性={importance:>6.2f}%")
    
    # 分析温度和湿度的总体影响
    temp_related = abs_coefficients[0] + abs_coefficients[2] + abs_coefficients[3]  # temp + temp*humi + temp²
    humi_related = abs_coefficients[1] + abs_coefficients[2] + abs_coefficients[4]  # humi + temp*humi + humi²
    
    total_influence = temp_related + humi_related
    temp_influence_pct = temp_related / total_influence * 100
    humi_influence_pct = humi_related / total_influence * 100
    
    print(f"\n总体影响分析:")
    print(f"温度相关项总影响: {temp_influence_pct:.2f}%")
    print(f"湿度相关项总影响: {humi_influence_pct:.2f}%")
    
    # 绘制系数重要性图
    plt.figure(figsize=(10, 6))
    bars = plt.bar(feature_names, relative_importance, color=['red', 'blue', 'green', 'orange', 'purple'])
    plt.xlabel('特征')
    plt.ylabel('相对重要性 (%)')
    plt.title('各特征对腐蚀电流的相对重要性')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bar, importance in zip(bars, relative_importance):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, 
                 f'{importance:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
else:
    # 如果最佳模型是线性模型
    coefficients = linear_model.coef_
    feature_names = ['temp', 'humi']
    
    abs_coefficients = np.abs(coefficients)
    relative_importance = abs_coefficients / np.sum(abs_coefficients) * 100
    
    print("各项的相对重要性:")
    for name, coef, importance in zip(feature_names, coefficients, relative_importance):
        print(f"{name:<12}: 系数={coef:>10.6f}, 重要性={importance:>6.2f}%")
    
    # 绘制系数重要性图
    plt.figure(figsize=(8, 6))
    bars = plt.bar(feature_names, relative_importance, color=['red', 'blue'])
    plt.xlabel('特征')
    plt.ylabel('相对重要性 (%)')
    plt.title('各特征对腐蚀电流的相对重要性')
    plt.grid(True, alpha=0.3)
    
    # 在柱状图上添加数值标签
    for bar, importance in zip(bars, relative_importance):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
                 f'{importance:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()

print("="*60)
print("分析结论:")
print("="*60)
print(f"1. 最佳预测模型: {best_model_name}")
print(f"2. 模型解释能力: R² = {best_r2:.4f} ({best_r2*100:.2f}% 的方差可以被解释)")

if best_model_name == '交互作用模型':
    print(f"3. 最佳函数关系:")
    print(f"   {equation}")
    print(f"4. 温度和湿度存在显著的交互作用效应")
    print(f"5. 温度相关项总影响: {temp_influence_pct:.2f}%")
    print(f"6. 湿度相关项总影响: {humi_influence_pct:.2f}%")
else:
    print(f"3. 最佳函数关系: 线性关系")
    print(f"   i1 = {linear_model.coef_[0]:.6f}*temp + {linear_model.coef_[1]:.6f}*humi + {linear_model.intercept_:.6f}")

print("\n建议:")
print("- 在腐蚀预测中，需要同时考虑温度和湿度的影响")
print("- 如果存在交互作用，温度和湿度的组合效应比单独效应更重要")
print("- 可以使用该模型进行腐蚀电流的预测和环境控制")