{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 温度湿度对腐蚀电流影响分析\n", "\n", "分析温度(temp)和湿度(humi)对腐蚀电流(i1)的影响，找到最佳的函数关系。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import PolynomialFeatures\n", "from sklearn.metrics import r2_score, mean_squared_error\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"库导入完成！\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载和预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取合并后的数据\n", "df = pd.read_csv('file/merged_hourly_data.csv')\n", "\n", "# 显示基本信息\n", "print(\"数据基本信息:\")\n", "print(f\"数据形状: {df.shape}\")\n", "print(f\"列名: {df.columns.tolist()}\")\n", "\n", "# 检查目标变量和特征变量\n", "target = 'i1'\n", "features = ['temp', 'humi']\n", "\n", "print(f\"\\n目标变量: {target}\")\n", "print(f\"特征变量: {features}\")\n", "\n", "# 检查缺失值\n", "print(f\"\\n缺失值统计:\")\n", "print(df[features + [target]].isnull().sum())\n", "\n", "# 删除包含缺失值的行\n", "df_clean = df[features + [target]].dropna()\n", "print(f\"\\n清理后的数据形状: {df_clean.shape}\")\n", "\n", "# 显示基本统计信息\n", "print(f\"\\n基本统计信息:\")\n", "display(df_clean.describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据可视化分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建图形\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 1. 温度与腐蚀电流的关系\n", "axes[0, 0].scatter(df_clean['temp'], df_clean['i1'], alpha=0.6, color='red')\n", "axes[0, 0].set_xlabel('温度 (°C)')\n", "axes[0, 0].set_ylabel('腐蚀电流 i1')\n", "axes[0, 0].set_title('温度 vs 腐蚀电流')\n", "axes[0, 0].grid(True, alpha=0.3)\n", "\n", "# 2. 湿度与腐蚀电流的关系\n", "axes[0, 1].scatter(df_clean['humi'], df_clean['i1'], alpha=0.6, color='blue')\n", "axes[0, 1].set_xlabel('湿度 (%)')\n", "axes[0, 1].set_ylabel('腐蚀电流 i1')\n", "axes[0, 1].set_title('湿度 vs 腐蚀电流')\n", "axes[0, 1].grid(True, alpha=0.3)\n", "\n", "# 3. 温度和湿度的散点图（颜色表示腐蚀电流）\n", "scatter = axes[1, 0].scatter(df_clean['temp'], df_clean['humi'], \n", "                            c=df_clean['i1'], cmap='viridis', alpha=0.6)\n", "axes[1, 0].set_xlabel('温度 (°C)')\n", "axes[1, 0].set_ylabel('湿度 (%)')\n", "axes[1, 0].set_title('温度-湿度关系（颜色表示腐蚀电流）')\n", "plt.colorbar(scatter, ax=axes[1, 0])\n", "\n", "# 4. 腐蚀电流的分布\n", "axes[1, 1].hist(df_clean['i1'], bins=50, alpha=0.7, color='green', edgecolor='black')\n", "axes[1, 1].set_xlabel('腐蚀电流 i1')\n", "axes[1, 1].set_ylabel('频次')\n", "axes[1, 1].set_title('腐蚀电流分布')\n", "axes[1, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 相关性分析\n", "correlation_matrix = df_clean[features + [target]].corr()\n", "print(f\"相关性矩阵:\")\n", "print(correlation_matrix)\n", "\n", "# 绘制相关性热力图\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('温度、湿度与腐蚀电流的相关性')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 多种函数关系建模"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 准备数据\n", "X = df_clean[features].values\n", "y = df_clean[target].values\n", "\n", "# 分割训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "# 存储不同模型的结果\n", "models_results = {}\n", "\n", "print(f\"训练集大小: {X_train.shape}\")\n", "print(f\"测试集大小: {X_test.shape}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 1. 线性模型: i1 = a*temp + b*humi + c\n", "print(\"1. 线性模型分析:\")\n", "linear_model = LinearRegression()\n", "linear_model.fit(X_train, y_train)\n", "y_pred_linear = linear_model.predict(X_test)\n", "r2_linear = r2_score(y_test, y_pred_linear)\n", "mse_linear = mean_squared_error(y_test, y_pred_linear)\n", "\n", "print(f\"线性模型系数: 温度={linear_model.coef_[0]:.6f}, 湿度={linear_model.coef_[1]:.6f}\")\n", "print(f\"截距: {linear_model.intercept_:.6f}\")\n", "print(f\"线性模型方程: i1 = {linear_model.coef_[0]:.6f}*temp + {linear_model.coef_[1]:.6f}*humi + {linear_model.intercept_:.6f}\")\n", "print(f\"R² = {r2_linear:.4f}, MSE = {mse_linear:.6f}\")\n", "\n", "models_results['线性模型'] = {'r2': r2_linear, 'mse': mse_linear, 'model': linear_model, 'predictions': y_pred_linear}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 2. 二次多项式模型\n", "print(\"\\n2. 二次多项式模型分析:\")\n", "poly2_features = PolynomialFeatures(degree=2, include_bias=False)\n", "X_train_poly2 = poly2_features.fit_transform(X_train)\n", "X_test_poly2 = poly2_features.transform(X_test)\n", "\n", "poly2_model = LinearRegression()\n", "poly2_model.fit(X_train_poly2, y_train)\n", "y_pred_poly2 = poly2_model.predict(X_test_poly2)\n", "r2_poly2 = r2_score(y_test, y_pred_poly2)\n", "mse_poly2 = mean_squared_error(y_test, y_pred_poly2)\n", "\n", "print(f\"二次多项式模型 R² = {r2_poly2:.4f}, MSE = {mse_poly2:.6f}\")\n", "print(f\"特征名称: {poly2_features.get_feature_names_out(['temp', 'humi'])}\")\n", "print(f\"系数: {poly2_model.coef_}\")\n", "\n", "models_results['二次多项式'] = {'r2': r2_poly2, 'mse': mse_poly2, 'model': poly2_model, 'predictions': y_pred_poly2}"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 3. 三次多项式模型\n", "print(\"\\n3. 三次多项式模型分析:\")\n", "poly3_features = PolynomialFeatures(degree=3, include_bias=False)\n", "X_train_poly3 = poly3_features.fit_transform(X_train)\n", "X_test_poly3 = poly3_features.transform(X_test)\n", "\n", "poly3_model = LinearRegression()\n", "poly3_model.fit(X_train_poly3, y_train)\n", "y_pred_poly3 = poly3_model.predict(X_test_poly3)\n", "r2_poly3 = r2_score(y_test, y_pred_poly3)\n", "mse_poly3 = mean_squared_error(y_test, y_pred_poly3)\n", "\n", "print(f\"三次多项式模型 R² = {r2_poly3:.4f}, MSE = {mse_poly3:.6f}\")\n", "\n", "models_results['三次多项式'] = {'r2': r2_poly3, 'mse': mse_poly3, 'model': poly3_model, 'predictions': y_pred_poly3}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 交互作用分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 4. 包含交互项的模型: i1 = a*temp + b*humi + c*temp*humi + d*temp² + e*humi² + f\n", "print(\"4. 交互作用模型分析:\")\n", "\n", "# 手动创建交互特征\n", "X_interaction = np.column_stack([\n", "    X,  # temp, humi\n", "    X[:, 0] * X[:, 1],  # temp * humi\n", "    X[:, 0] ** 2,  # temp²\n", "    X[:, 1] ** 2   # humi²\n", "])\n", "\n", "X_train_int, X_test_int, y_train_int, y_test_int = train_test_split(\n", "    X_interaction, y, test_size=0.2, random_state=42)\n", "\n", "interaction_model = LinearRegression()\n", "interaction_model.fit(X_train_int, y_train_int)\n", "y_pred_int = interaction_model.predict(X_test_int)\n", "r2_int = r2_score(y_test_int, y_pred_int)\n", "mse_int = mean_squared_error(y_test_int, y_pred_int)\n", "\n", "feature_names = ['temp', 'humi', 'temp*humi', 'temp²', 'humi²']\n", "print(f\"交互作用模型系数:\")\n", "for i, (name, coef) in enumerate(zip(feature_names, interaction_model.coef_)):\n", "    print(f\"  {name}: {coef:.6f}\")\n", "print(f\"截距: {interaction_model.intercept_:.6f}\")\n", "print(f\"R² = {r2_int:.4f}, MSE = {mse_int:.6f}\")\n", "\n", "models_results['交互作用模型'] = {'r2': r2_int, 'mse': mse_int, 'model': interaction_model, 'predictions': y_pred_int}\n", "\n", "# 构建完整的交互作用方程\n", "equation = f\"i1 = {interaction_model.coef_[0]:.6f}*temp + {interaction_model.coef_[1]:.6f}*humi + {interaction_model.coef_[2]:.6f}*temp*humi + {interaction_model.coef_[3]:.6f}*temp² + {interaction_model.coef_[4]:.6f}*humi² + {interaction_model.intercept_:.6f}\"\n", "print(f\"\\n完整的交互作用方程:\")\n", "print(equation)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 模型比较和最佳模型选择"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 比较所有模型\n", "print(f\"模型性能比较:\")\n", "print(\"=\"*60)\n", "print(f\"{'模型名称':<15} {'R²':<10} {'MSE':<15}\")\n", "print(\"=\"*60)\n", "\n", "best_model_name = None\n", "best_r2 = -np.inf\n", "\n", "for name, results in models_results.items():\n", "    print(f\"{name:<15} {results['r2']:<10.4f} {results['mse']:<15.6f}\")\n", "    if results['r2'] > best_r2:\n", "        best_r2 = results['r2']\n", "        best_model_name = name\n", "\n", "print(\"=\"*60)\n", "print(f\"最佳模型: {best_model_name} (R² = {best_r2:.4f})\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 绘制预测值 vs 实际值的对比图\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "axes = axes.ravel()\n", "\n", "model_predictions = [\n", "    ('线性模型', models_results['线性模型']['predictions']),\n", "    ('二次多项式', models_results['二次多项式']['predictions']),\n", "    ('三次多项式', models_results['三次多项式']['predictions']),\n", "    ('交互作用模型', models_results['交互作用模型']['predictions'])\n", "]\n", "\n", "for i, (name, y_pred) in enumerate(model_predictions):\n", "    # 使用对应的测试集\n", "    if name == '交互作用模型':\n", "        y_test_plot = y_test_int\n", "    else:\n", "        y_test_plot = y_test\n", "        \n", "    axes[i].scatter(y_test_plot, y_pred, alpha=0.6)\n", "    axes[i].plot([y_test_plot.min(), y_test_plot.max()], [y_test_plot.min(), y_test_plot.max()], 'r--', lw=2)\n", "    axes[i].set_xlabel('实际值')\n", "    axes[i].set_ylabel('预测值')\n", "    axes[i].set_title(f'{name}\\nR² = {models_results[name][\"r2\"]:.4f}')\n", "    axes[i].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 影响因子分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析温度和湿度的相对重要性\n", "print(f\"影响因子分析:\")\n", "print(\"=\"*50)\n", "\n", "# 使用最佳模型进行分析\n", "if best_model_name == '交互作用模型':\n", "    coefficients = interaction_model.coef_\n", "    feature_names = ['temp', 'humi', 'temp*humi', 'temp²', 'humi²']\n", "    \n", "    # 计算各项的相对重要性（基于系数的绝对值）\n", "    abs_coefficients = np.abs(coefficients)\n", "    relative_importance = abs_coefficients / np.sum(abs_coefficients) * 100\n", "    \n", "    print(\"各项的相对重要性:\")\n", "    for name, coef, importance in zip(feature_names, coefficients, relative_importance):\n", "        print(f\"{name:<12}: 系数={coef:>10.6f}, 重要性={importance:>6.2f}%\")\n", "    \n", "    # 分析温度和湿度的总体影响\n", "    temp_related = abs_coefficients[0] + abs_coefficients[2] + abs_coefficients[3]  # temp + temp*humi + temp²\n", "    humi_related = abs_coefficients[1] + abs_coefficients[2] + abs_coefficients[4]  # humi + temp*humi + humi²\n", "    \n", "    total_influence = temp_related + humi_related\n", "    temp_influence_pct = temp_related / total_influence * 100\n", "    humi_influence_pct = humi_related / total_influence * 100\n", "    \n", "    print(f\"\\n总体影响分析:\")\n", "    print(f\"温度相关项总影响: {temp_influence_pct:.2f}%\")\n", "    print(f\"湿度相关项总影响: {humi_influence_pct:.2f}%\")\n", "    \n", "    # 绘制系数重要性图\n", "    plt.figure(figsize=(10, 6))\n", "    bars = plt.bar(feature_names, relative_importance, color=['red', 'blue', 'green', 'orange', 'purple'])\n", "    plt.xlabel('特征')\n", "    plt.ylabel('相对重要性 (%)')\n", "    plt.title('各特征对腐蚀电流的相对重要性')\n", "    plt.xticks(rotation=45)\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 在柱状图上添加数值标签\n", "    for bar, importance in zip(bars, relative_importance):\n", "        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.5, \n", "                 f'{importance:.1f}%', ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "else:\n", "    # 如果最佳模型是线性模型\n", "    coefficients = linear_model.coef_\n", "    feature_names = ['temp', 'humi']\n", "    \n", "    abs_coefficients = np.abs(coefficients)\n", "    relative_importance = abs_coefficients / np.sum(abs_coefficients) * 100\n", "    \n", "    print(\"各项的相对重要性:\")\n", "    for name, coef, importance in zip(feature_names, coefficients, relative_importance):\n", "        print(f\"{name:<12}: 系数={coef:>10.6f}, 重要性={importance:>6.2f}%\")\n", "    \n", "    # 绘制系数重要性图\n", "    plt.figure(figsize=(8, 6))\n", "    bars = plt.bar(feature_names, relative_importance, color=['red', 'blue'])\n", "    plt.xlabel('特征')\n", "    plt.ylabel('相对重要性 (%)')\n", "    plt.title('各特征对腐蚀电流的相对重要性')\n", "    plt.grid(True, alpha=0.3)\n", "    \n", "    # 在柱状图上添加数值标签\n", "    for bar, importance in zip(bars, relative_importance):\n", "        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, \n", "                 f'{importance:.1f}%', ha='center', va='bottom')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 结论和建议"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=\"*60)\n", "print(\"分析结论:\")\n", "print(\"=\"*60)\n", "print(f\"1. 最佳预测模型: {best_model_name}\")\n", "print(f\"2. 模型解释能力: R² = {best_r2:.4f} ({best_r2*100:.2f}% 的方差可以被解释)\")\n", "\n", "if best_model_name == '交互作用模型':\n", "    print(f\"3. 最佳函数关系:\")\n", "    print(f\"   {equation}\")\n", "    print(f\"4. 温度和湿度存在显著的交互作用效应\")\n", "    print(f\"5. 温度相关项总影响: {temp_influence_pct:.2f}%\")\n", "    print(f\"6. 湿度相关项总影响: {humi_influence_pct:.2f}%\")\n", "else:\n", "    print(f\"3. 最佳函数关系: 线性关系\")\n", "    print(f\"   i1 = {linear_model.coef_[0]:.6f}*temp + {linear_model.coef_[1]:.6f}*humi + {linear_model.intercept_:.6f}\")\n", "\n", "print(\"\\n建议:\")\n", "print(\"- 在腐蚀预测中，需要同时考虑温度和湿度的影响\")\n", "print(\"- 如果存在交互作用，温度和湿度的组合效应比单独效应更重要\")\n", "print(\"- 可以使用该模型进行腐蚀电流的预测和环境控制\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}