#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键生成所有腐蚀分析图片
包含传统机器学习分析和双重机器学习因果分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from sklearn.model_selection import train_test_split, cross_val_score, KFold
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from sklearn.impute import KNNImputer
from scipy.stats import pearsonr
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 确保输出目录存在
os.makedirs('file/photo', exist_ok=True)

def load_and_prepare_data():
    """加载和预处理数据"""
    print("正在加载和预处理数据...")
    
    # 读取数据
    df = pd.read_csv('file/merged_hourly_data.csv')
    
    # 定义变量
    feature_cols = [col for col in df.columns if col not in ['time_points', 'i1']]
    target_col = 'i1'
    
    # 处理缺失值
    df_clean = df.dropna(subset=[target_col]).copy()
    imputer = KNNImputer(n_neighbors=5)
    df_clean[feature_cols] = imputer.fit_transform(df_clean[feature_cols])
    
    print(f"数据形状: {df_clean.shape}")
    print(f"特征数量: {len(feature_cols)}")
    
    return df_clean, feature_cols, target_col

def generate_correlation_analysis(df_clean, feature_cols, target_col):
    """生成相关性分析图"""
    print("生成相关性分析图...")
    
    # 计算相关性
    correlations = df_clean[feature_cols].corrwith(df_clean[target_col]).abs().sort_values(ascending=False)
    
    # 相关性条形图
    plt.figure(figsize=(12, 8))
    bars = plt.bar(range(len(correlations)), correlations.values, 
                   color=['red' if x > 0.1 else 'gray' for x in correlations.values])
    plt.xlabel('环境特征')
    plt.ylabel('与腐蚀电流的相关系数(绝对值)')
    plt.title('各环境特征与腐蚀电流的相关性分析')
    plt.xticks(range(len(correlations)), correlations.index, rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, corr) in enumerate(zip(bars, correlations.values)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{corr:.3f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.savefig('file/photo/correlation_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return correlations

def generate_feature_relationships(df_clean, feature_cols, target_col, top_n=6):
    """生成特征关系图"""
    print("生成特征关系图...")
    
    # 选择相关性最高的特征
    correlations = df_clean[feature_cols].corrwith(df_clean[target_col]).abs()
    top_features = correlations.nlargest(top_n).index.tolist()
    
    # 创建子图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.ravel()
    
    for i, feature in enumerate(top_features):
        if i < len(axes):
            axes[i].scatter(df_clean[feature], df_clean[target_col], alpha=0.6, s=20)
            axes[i].set_xlabel(feature)
            axes[i].set_ylabel('腐蚀电流 i1')
            axes[i].set_title(f'{feature} vs 腐蚀电流\n相关系数: {correlations[feature]:.4f}')
            axes[i].grid(True, alpha=0.3)
            
            # 添加趋势线
            z = np.polyfit(df_clean[feature].dropna(), 
                          df_clean[target_col][df_clean[feature].notna()], 1)
            p = np.poly1d(z)
            axes[i].plot(df_clean[feature], p(df_clean[feature]), "r--", alpha=0.8)
    
    plt.tight_layout()
    plt.savefig('file/photo/feature_relationships.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return top_features

def build_ml_models(df_clean, feature_cols, target_col):
    """构建机器学习模型"""
    print("构建机器学习模型...")
    
    # 准备数据
    X = df_clean[feature_cols]
    y = df_clean[target_col]
    
    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X_scaled, y, test_size=0.2, random_state=42
    )
    
    # 定义模型
    models = {
        '线性回归': LinearRegression(),
        'Ridge回归': Ridge(alpha=1.0),
        'Lasso回归': Lasso(alpha=0.1),
        'ElasticNet': ElasticNet(alpha=0.1, l1_ratio=0.5),
        '随机森林': RandomForestRegressor(n_estimators=100, random_state=42),
        '梯度提升': GradientBoostingRegressor(n_estimators=100, random_state=42)
    }
    
    # 训练和评估
    results = {}
    for name, model in models.items():
        model.fit(X_train, y_train)
        
        y_pred_train = model.predict(X_train)
        y_pred_test = model.predict(X_test)
        
        train_r2 = r2_score(y_train, y_pred_train)
        test_r2 = r2_score(y_test, y_pred_test)
        test_mse = mean_squared_error(y_test, y_pred_test)
        
        cv_scores = cross_val_score(model, X_scaled, y, cv=5, scoring='r2')
        
        results[name] = {
            'model': model,
            'train_r2': train_r2,
            'test_r2': test_r2,
            'test_mse': test_mse,
            'cv_mean': cv_scores.mean(),
            'cv_std': cv_scores.std(),
            'predictions': y_pred_test,
            'y_test': y_test
        }
    
    return results, feature_cols

def generate_model_comparison(results):
    """生成模型对比图"""
    print("生成模型对比图...")
    
    model_names = list(results.keys())
    train_r2 = [results[name]['train_r2'] for name in model_names]
    test_r2 = [results[name]['test_r2'] for name in model_names]
    cv_mean = [results[name]['cv_mean'] for name in model_names]
    cv_std = [results[name]['cv_std'] for name in model_names]
    
    # 创建图形
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # R²对比
    x = np.arange(len(model_names))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, train_r2, width, label='训练R²', alpha=0.8)
    axes[0, 0].bar(x + width/2, test_r2, width, label='测试R²', alpha=0.8)
    axes[0, 0].set_xlabel('模型')
    axes[0, 0].set_ylabel('R² 分数')
    axes[0, 0].set_title('模型R²性能对比')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(model_names, rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 交叉验证结果
    axes[0, 1].errorbar(x, cv_mean, yerr=cv_std, fmt='o', capsize=5, capthick=2)
    axes[0, 1].set_xlabel('模型')
    axes[0, 1].set_ylabel('交叉验证 R²')
    axes[0, 1].set_title('交叉验证性能')
    axes[0, 1].set_xticks(x)
    axes[0, 1].set_xticklabels(model_names, rotation=45)
    axes[0, 1].grid(True, alpha=0.3)
    
    # 最佳模型预测vs实际
    best_model_name = max(results.keys(), key=lambda k: results[k]['cv_mean'])
    best_result = results[best_model_name]
    
    axes[1, 0].scatter(best_result['y_test'], best_result['predictions'], alpha=0.6)
    min_val = min(best_result['y_test'].min(), best_result['predictions'].min())
    max_val = max(best_result['y_test'].max(), best_result['predictions'].max())
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'r--', lw=2)
    axes[1, 0].set_xlabel('实际值')
    axes[1, 0].set_ylabel('预测值')
    axes[1, 0].set_title(f'{best_model_name} - 预测vs实际\nR² = {best_result["test_r2"]:.4f}')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 残差图
    residuals = best_result['y_test'] - best_result['predictions']
    axes[1, 1].scatter(best_result['predictions'], residuals, alpha=0.6)
    axes[1, 1].axhline(y=0, color='r', linestyle='--')
    axes[1, 1].set_xlabel('预测值')
    axes[1, 1].set_ylabel('残差')
    axes[1, 1].set_title(f'{best_model_name} - 残差分析')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('file/photo/model_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return best_model_name

def generate_feature_importance(results, feature_cols, best_model_name):
    """生成特征重要性图"""
    print("生成特征重要性图...")
    
    best_model = results[best_model_name]['model']
    
    # 获取特征重要性
    if hasattr(best_model, 'feature_importances_'):
        importances = best_model.feature_importances_
        importance_type = "特征重要性"
    elif hasattr(best_model, 'coef_'):
        importances = np.abs(best_model.coef_)
        importance_type = "系数绝对值"
    else:
        print("该模型不支持特征重要性分析")
        return
    
    # 创建DataFrame
    feature_importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': importances
    }).sort_values('importance', ascending=False)
    
    # 绘制图表
    plt.figure(figsize=(12, 8))
    top_features = feature_importance_df.head(10)
    
    bars = plt.barh(range(len(top_features)), top_features['importance'])
    plt.yticks(range(len(top_features)), top_features['feature'])
    plt.xlabel(importance_type)
    plt.title(f'{best_model_name} - 前10个重要特征')
    plt.gca().invert_yaxis()
    
    # 添加数值标签
    for i, bar in enumerate(bars):
        width = bar.get_width()
        plt.text(width, bar.get_y() + bar.get_height()/2, 
                f'{width:.4f}', ha='left', va='center')
    
    plt.tight_layout()
    plt.savefig('file/photo/feature_importance.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return feature_importance_df

def double_machine_learning_analysis(df_clean, feature_cols, target_col, treatment_feature):
    """双重机器学习分析"""
    print(f"分析 {treatment_feature} 的因果效应...")
    
    # 准备数据
    control_features = [col for col in feature_cols if col != treatment_feature]
    X_control = df_clean[control_features]
    T = df_clean[treatment_feature]
    Y = df_clean[target_col]
    
    # 标准化
    scaler_control = StandardScaler()
    X_control_scaled = scaler_control.fit_transform(X_control)
    
    scaler_treatment = StandardScaler()
    T_scaled = scaler_treatment.fit_transform(T.values.reshape(-1, 1)).flatten()
    
    # 交叉拟合
    n_folds = 5
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    Y_residuals = np.zeros(len(Y))
    T_residuals = np.zeros(len(T))
    
    for fold, (train_idx, test_idx) in enumerate(kf.split(X_control_scaled)):
        X_train, X_test = X_control_scaled[train_idx], X_control_scaled[test_idx]
        T_train, T_test = T_scaled[train_idx], T_scaled[test_idx]
        Y_train, Y_test = Y.iloc[train_idx], Y.iloc[test_idx]
        
        # 预测Y
        model_Y = RandomForestRegressor(n_estimators=50, random_state=42)
        model_Y.fit(X_train, Y_train)
        Y_pred = model_Y.predict(X_test)
        Y_residuals[test_idx] = Y_test - Y_pred
        
        # 预测T
        model_T = RandomForestRegressor(n_estimators=50, random_state=42)
        model_T.fit(X_train, T_train)
        T_pred = model_T.predict(X_test)
        T_residuals[test_idx] = T_test - T_pred
    
    # 分析残差关系
    causal_model = LinearRegression()
    causal_model.fit(T_residuals.reshape(-1, 1), Y_residuals)
    
    causal_effect = causal_model.coef_[0]
    r2_causal = causal_model.score(T_residuals.reshape(-1, 1), Y_residuals)
    _, p_value = pearsonr(T_residuals, Y_residuals)
    
    # 计算置信区间
    n = len(T_residuals)
    mse = np.mean((Y_residuals - causal_model.predict(T_residuals.reshape(-1, 1)))**2)
    var_T = np.var(T_residuals)
    se_beta = np.sqrt(mse / (var_T * (n - 1)))
    
    t_critical = stats.t.ppf(0.975, n - 2)
    ci_lower = causal_effect - t_critical * se_beta
    ci_upper = causal_effect + t_critical * se_beta
    
    # 显著性
    if p_value < 0.05:
        significance = "显著"
    else:
        significance = "不显著"
    
    return {
        'treatment_feature': treatment_feature,
        'causal_effect': causal_effect,
        'confidence_interval': (ci_lower, ci_upper),
        'r2': r2_causal,
        'p_value': p_value,
        'significance': significance,
        'T_residuals': T_residuals,
        'Y_residuals': Y_residuals
    }

def generate_causal_analysis(df_clean, feature_cols, target_col, top_features):
    """生成因果分析图"""
    print("生成因果分析图...")
    
    # 对前5个特征进行因果分析
    causal_results = {}
    for feature in top_features[:5]:
        result = double_machine_learning_analysis(df_clean, feature_cols, target_col, feature)
        causal_results[feature] = result
    
    # 生成综合因果效应图
    features = list(causal_results.keys())
    effects = [causal_results[f]['causal_effect'] for f in features]
    p_values = [causal_results[f]['p_value'] for f in features]
    colors = ['red' if p < 0.05 else 'gray' for p in p_values]
    
    plt.figure(figsize=(12, 8))
    bars = plt.bar(range(len(features)), effects, color=colors, alpha=0.7, edgecolor='black')
    
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.xlabel('环境特征')
    plt.ylabel('因果效应估计')
    plt.title('各环境因素对腐蚀电流的因果效应\n(红色=显著, 灰色=不显著)')
    plt.xticks(range(len(features)), features, rotation=45)
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, (bar, effect) in enumerate(zip(bars, effects)):
        plt.text(bar.get_x() + bar.get_width()/2, 
                bar.get_height() + 0.001 if effect > 0 else bar.get_height() - 0.001,
                f'{effect:.4f}', ha='center', va='bottom' if effect > 0 else 'top')
    
    plt.tight_layout()
    plt.savefig('file/photo/causal_effects.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 生成各特征的残差分析图
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    axes = axes.ravel()
    
    for i, (feature, result) in enumerate(causal_results.items()):
        if i < len(axes):
            T_residuals = result['T_residuals']
            Y_residuals = result['Y_residuals']
            causal_effect = result['causal_effect']
            p_value = result['p_value']
            
            axes[i].scatter(T_residuals, Y_residuals, alpha=0.6, s=20)
            
            # 拟合线
            T_range = np.linspace(T_residuals.min(), T_residuals.max(), 100)
            Y_fit = causal_effect * T_range
            axes[i].plot(T_range, Y_fit, 'r-', linewidth=2)
            
            axes[i].set_xlabel(f'{feature} 残差')
            axes[i].set_ylabel('腐蚀电流 残差')
            axes[i].set_title(f'{feature}\n因果效应: {causal_effect:.6f}\np值: {p_value:.4f}')
            axes[i].grid(True, alpha=0.3)
    
    # 隐藏多余的子图
    for i in range(len(causal_results), len(axes)):
        axes[i].set_visible(False)
    
    plt.tight_layout()
    plt.savefig('file/photo/residual_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return causal_results

if __name__ == "__main__":
    print("开始生成所有腐蚀分析图片...")
    print("="*50)
    
    # 1. 加载数据
    df_clean, feature_cols, target_col = load_and_prepare_data()
    
    # 2. 相关性分析
    correlations = generate_correlation_analysis(df_clean, feature_cols, target_col)
    
    # 3. 特征关系图
    top_features = generate_feature_relationships(df_clean, feature_cols, target_col)
    
    # 4. 机器学习模型
    results, feature_cols = build_ml_models(df_clean, feature_cols, target_col)
    
    # 5. 模型对比
    best_model_name = generate_model_comparison(results)
    
    # 6. 特征重要性
    feature_importance_df = generate_feature_importance(results, feature_cols, best_model_name)
    
    # 7. 因果分析
    causal_results = generate_causal_analysis(df_clean, feature_cols, target_col, top_features)
    
    print("\n" + "="*50)
    print("所有分析图片生成完成！")
    print("="*50)
    print("生成的文件:")
    print("- file/photo/correlation_analysis.png: 相关性分析")
    print("- file/photo/feature_relationships.png: 特征关系图")
    print("- file/photo/model_comparison.png: 模型对比图")
    print("- file/photo/feature_importance.png: 特征重要性图")
    print("- file/photo/causal_effects.png: 因果效应图")
    print("- file/photo/residual_analysis.png: 残差分析图")
    
    # 输出关键结果
    print(f"\n关键结果:")
    print(f"- 最佳预测模型: {best_model_name}")
    print(f"- 最重要特征: {feature_importance_df.iloc[0]['feature']}")
    
    significant_causal = [f for f, r in causal_results.items() if r['p_value'] < 0.05]
    if significant_causal:
        print(f"- 显著因果因素: {', '.join(significant_causal)}")
    else:
        print("- 未发现显著的因果因素")
