import pandas as pd
import numpy as np

# 读取两个数据文件
df_final = pd.read_csv("file/merged_final_data.csv")
df_hourly = pd.read_csv("file/process_data2.csv")

# 确保时间列是datetime类型
df_final["datetime"] = pd.to_datetime(df_final["datetime"])
df_hourly["time_points"] = pd.to_datetime(df_hourly["time_points"])

# 需要检查的列
columns_to_check = [
    "temp",
    "humi",
    "airpress",
    "condensation",
    "H2S",
    "SO2",
    "PM1_0",
    "PM2_5",
    "PM10",
    "NO2",
    "CO2",
    "O3",
    "illuminance",
]

# 打印两个数据集的前几行
print("原始数据前5行：")
print(df_final[["datetime"] + columns_to_check].head())
print("\n补充数据前5行：")
print(df_hourly[["time_points"] + columns_to_check].head())

# 检查特定时间点
test_time = pd.Timestamp("2023-12-01 21:00:00")
print(f"\n检查时间点 {test_time} 的数据：")
print("\n原始数据：")
print(df_final[df_final["datetime"] == test_time][["datetime"] + columns_to_check])
print("\n补充数据：")
print(
    df_hourly[df_hourly["time_points"] == test_time][["time_points"] + columns_to_check]
)

# 检查数据类型
print("\n原始数据类型：")
print(df_final[columns_to_check].dtypes)
print("\n补充数据类型：")
print(df_hourly[columns_to_check].dtypes)

# 检查数值范围
print("\n补充数据数值范围：")
for col in columns_to_check:
    if col in df_hourly.columns:
        print(f"{col}:")
        print(f"  最小值: {df_hourly[col].min()}")
        print(f"  最大值: {df_hourly[col].max()}")
        print(f"  非空值数量: {df_hourly[col].count()}")
        print(f"  空值数量: {df_hourly[col].isna().sum()}")
        print()

# 检查时间范围
print("\n时间范围检查：")
print("原始数据时间范围：")
print("开始：", df_final["datetime"].min())
print("结束：", df_final["datetime"].max())
print("\n补充数据时间范围：")
print("开始：", df_hourly["time_points"].min())
print("结束：", df_hourly["time_points"].max())

# 检查是否有重复的时间点
print("\n重复时间点检查：")
print("原始数据重复时间点数：", df_final["datetime"].duplicated().sum())
print("补充数据重复时间点数：", df_hourly["time_points"].duplicated().sum())

# 检查时间点的对应关系
print("\n时间点对应关系检查：")
test_times = df_final["datetime"].head(5)
for time in test_times:
    print(f"\n时间点: {time}")
    print("原始数据：")
    print(df_final[df_final["datetime"] == time][columns_to_check].iloc[0])
    matching_data = df_hourly[df_hourly["time_points"] == time]
    if not matching_data.empty:
        print("补充数据：")
        print(matching_data[columns_to_check].iloc[0])
    else:
        print("补充数据中没有对应的时间点")

# 统计每列的缺失值数量
missing_counts = df_final[columns_to_check].isna().sum()
print("\n每列缺失值数量：")
print(missing_counts[missing_counts > 0])  # 只显示有缺失值的列

# 检查两个数据集的列名
print("\n原始数据列名：")
print(df_final.columns.tolist())
print("\n补充数据列名：")
print(df_hourly.columns.tolist())
