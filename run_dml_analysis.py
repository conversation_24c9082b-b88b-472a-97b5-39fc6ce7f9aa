#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行双重机器学习分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.model_selection import KFold
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.impute import KNNImputer
from scipy.stats import pearsonr
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

def quick_dml_analysis(treatment_feature='temp'):
    """
    快速双重机器学习分析

    参数:
    treatment_feature: 要分析因果效应的特征名称
    """
    print(f"开始双重机器学习分析: {treatment_feature} 对腐蚀电流的因果效应")
    print("="*70)

    # 1. 加载数据
    print("1. 加载数据...")
    df = pd.read_csv('file/merged_hourly_data.csv')

    # 定义变量
    feature_cols = [col for col in df.columns if col not in ['time_points', 'i1']]
    target_col = 'i1'

    # 处理缺失值
    df_clean = df.dropna(subset=[target_col]).copy()
    imputer = KNNImputer(n_neighbors=5)
    df_clean[feature_cols] = imputer.fit_transform(df_clean[feature_cols])

    print(f"数据形状: {df_clean.shape}")

    # 2. 准备变量
    print("2. 准备变量...")
    control_features = [col for col in feature_cols if col != treatment_feature]

    X_control = df_clean[control_features]
    T = df_clean[treatment_feature]
    Y = df_clean[target_col]

    # 标准化
    scaler_control = StandardScaler()
    X_control_scaled = scaler_control.fit_transform(X_control)

    scaler_treatment = StandardScaler()
    T_scaled = scaler_treatment.fit_transform(T.values.reshape(-1, 1)).flatten()

    # 3. 交叉拟合计算残差
    print("3. 计算残差...")
    n_folds = 5
    kf = KFold(n_splits=n_folds, shuffle=True, random_state=42)

    Y_residuals = np.zeros(len(Y))
    T_residuals = np.zeros(len(T))

    for fold, (train_idx, test_idx) in enumerate(kf.split(X_control_scaled)):
        # 训练集和测试集
        X_train, X_test = X_control_scaled[train_idx], X_control_scaled[test_idx]
        T_train, T_test = T_scaled[train_idx], T_scaled[test_idx]
        Y_train, Y_test = Y.iloc[train_idx], Y.iloc[test_idx]

        # 预测Y
        model_Y = RandomForestRegressor(n_estimators=50, random_state=42)
        model_Y.fit(X_train, Y_train)
        Y_pred = model_Y.predict(X_test)
        Y_residuals[test_idx] = Y_test - Y_pred

        # 预测T
        model_T = RandomForestRegressor(n_estimators=50, random_state=42)
        model_T.fit(X_train, T_train)
        T_pred = model_T.predict(X_test)
        T_residuals[test_idx] = T_test - T_pred

    # 4. 分析残差关系
    print("4. 分析因果效应...")
    causal_model = LinearRegression()
    causal_model.fit(T_residuals.reshape(-1, 1), Y_residuals)

    causal_effect = causal_model.coef_[0]
    r2_causal = causal_model.score(T_residuals.reshape(-1, 1), Y_residuals)
    _, p_value = pearsonr(T_residuals, Y_residuals)

    # 计算置信区间
    n = len(T_residuals)
    mse = np.mean((Y_residuals - causal_model.predict(T_residuals.reshape(-1, 1)))**2)
    var_T = np.var(T_residuals)
    se_beta = np.sqrt(mse / (var_T * (n - 1)))

    t_critical = stats.t.ppf(0.975, n - 2)
    ci_lower = causal_effect - t_critical * se_beta
    ci_upper = causal_effect + t_critical * se_beta

    # 5. 输出结果
    print("\n" + "="*50)
    print("双重机器学习结果:")
    print("="*50)
    print(f"处理变量: {treatment_feature}")
    print(f"因果效应估计: {causal_effect:.6f}")
    print(f"标准误差: {se_beta:.6f}")
    print(f"95%置信区间: [{ci_lower:.6f}, {ci_upper:.6f}]")
    print(f"R²: {r2_causal:.4f}")
    print(f"p值: {p_value:.6f}")

    # 显著性
    if p_value < 0.001:
        significance = "极显著 (p < 0.001)"
    elif p_value < 0.01:
        significance = "高度显著 (p < 0.01)"
    elif p_value < 0.05:
        significance = "显著 (p < 0.05)"
    else:
        significance = "不显著 (p >= 0.05)"

    print(f"统计显著性: {significance}")

    # 实际意义
    treatment_std = df_clean[treatment_feature].std()
    print(f"\n实际意义解释:")
    print(f"- {treatment_feature}增加1个标准差({treatment_std:.4f})时")
    print(f"- 腐蚀电流平均变化{causal_effect * treatment_std:.6f}单位")

    # 6. 可视化
    print("\n5. 生成可视化...")
    plt.figure(figsize=(12, 5))

    # 残差散点图
    plt.subplot(1, 2, 1)
    plt.scatter(T_residuals, Y_residuals, alpha=0.6, s=20)

    # 拟合线
    T_range = np.linspace(T_residuals.min(), T_residuals.max(), 100)
    Y_fit = causal_effect * T_range
    plt.plot(T_range, Y_fit, 'r-', linewidth=2,
             label=f'因果效应: {causal_effect:.6f}')

    plt.xlabel(f'{treatment_feature} 残差（去除混淆变量影响）')
    plt.ylabel('腐蚀电流 残差（去除混淆变量影响）')
    plt.title(f'双重机器学习：{treatment_feature} → 腐蚀电流\n因果效应 = {causal_effect:.6f} (p = {p_value:.4f})')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 残差分布对比
    plt.subplot(1, 2, 2)
    plt.hist(T_residuals, bins=30, alpha=0.5, label=f'{treatment_feature} 残差', color='blue')
    plt.hist(Y_residuals, bins=30, alpha=0.5, label='腐蚀电流 残差', color='red')
    plt.xlabel('残差值')
    plt.ylabel('频次')
    plt.title('残差分布')
    plt.legend()
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'file/photo/quick_dml_{treatment_feature}.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 7. 与传统相关性对比
    traditional_corr = df_clean[treatment_feature].corr(df_clean[target_col])

    print(f"\n传统分析 vs 因果分析对比:")
    print(f"- 传统相关系数: {traditional_corr:.6f}")
    print(f"- 因果效应估计: {causal_effect:.6f}")
    print(f"- 差异: {abs(traditional_corr) - abs(causal_effect):.6f}")

    if abs(traditional_corr) > abs(causal_effect):
        print("- 传统相关性可能高估了真实的因果效应")
    elif abs(traditional_corr) < abs(causal_effect):
        print("- 传统相关性可能低估了真实的因果效应")
    else:
        print("- 传统相关性与因果效应基本一致")

    return {
        'treatment_feature': treatment_feature,
        'causal_effect': causal_effect,
        'p_value': p_value,
        'confidence_interval': (ci_lower, ci_upper),
        'traditional_correlation': traditional_corr,
        'significance': significance
    }

def analyze_multiple_features():
    """分析多个特征的因果效应"""
    print("分析多个环境因素的因果效应")
    print("="*50)

    # 读取数据并选择重要特征
    df = pd.read_csv('file/merged_hourly_data.csv')
    feature_cols = [col for col in df.columns if col not in ['time_points', 'i1']]
    target_col = 'i1'

    # 处理缺失值
    df_clean = df.dropna(subset=[target_col]).copy()
    imputer = KNNImputer(n_neighbors=5)
    df_clean[feature_cols] = imputer.fit_transform(df_clean[feature_cols])

    # 选择相关性最高的5个特征
    correlations = df_clean[feature_cols].corrwith(df_clean[target_col]).abs()
    top_features = correlations.nlargest(5).index.tolist()

    print(f"分析的特征: {top_features}")

    results = {}
    for feature in top_features:
        print(f"\n{'='*30}")
        result = quick_dml_analysis(feature)
        results[feature] = result

    # 汇总结果
    print(f"\n" + "="*80)
    print("因果效应汇总")
    print("="*80)
    print(f"{'特征':<15} {'因果效应':<12} {'p值':<10} {'显著性':<15} {'传统相关性':<12}")
    print("-"*80)

    for feature, result in results.items():
        print(f"{feature:<15} {result['causal_effect']:<12.6f} {result['p_value']:<10.6f} "
              f"{result['significance']:<15} {result['traditional_correlation']:<12.6f}")

    return results

if __name__ == "__main__":
    print("双重机器学习腐蚀分析")
    print("="*50)

    # 选择分析模式
    mode = input("选择分析模式:\n1. 单个特征分析 (输入特征名，如 'temp')\n2. 多特征分析 (输入 'all')\n请输入: ").strip()

    if mode.lower() == 'all':
        results = analyze_multiple_features()
    else:
        # 单个特征分析
        feature_name = mode if mode in ['temp', 'humi', 'airpress', 'condensation', 'H2S', 'SO2', 'PM1_0', 'PM2_5', 'PM10', 'NO2', 'CO2', 'O3', 'illuminance'] else 'temp'
        result = quick_dml_analysis(feature_name)

    print(f"\n分析完成！生成的文件保存在 file/ 目录下。")
