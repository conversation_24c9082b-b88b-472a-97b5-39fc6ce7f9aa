import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
from pathlib import Path
import os
import ctypes
from tkinter.font import Font


class DataTransformGUI:
    def __init__(self, root):
        # 启用高DPI支持
        try:
            ctypes.windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        self.root = root
        self.root.title("环境数据转换工具")

        # 设置默认字体
        default_font = Font(family="Microsoft YaHei UI", size=36)
        self.root.option_add("*Font", default_font)

        # 设置固定窗口大小
        window_width = 1800
        window_height = 1000
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # 禁止调整窗口大小
        self.root.resizable(False, False)

        # 设置默认目录为当前目录
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # 创建主框架
        main_frame = ttk.Frame(root, padding="50")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置主窗口的网格权重
        root.grid_rowconfigure(0, weight=1)
        root.grid_columnconfigure(0, weight=1)

        # 配置主框架的网格权重
        main_frame.grid_columnconfigure(0, weight=1)

        # 设置样式
        style = ttk.Style()
        style.configure("Large.TLabel", font=("Microsoft YaHei UI", 36))
        style.configure("Large.TButton", font=("Microsoft YaHei UI", 36))
        style.configure("Large.TEntry", font=("Microsoft YaHei UI", 36))

        # 站点选择
        ttk.Label(main_frame, text="站点代码:", style="Large.TLabel").grid(
            row=0, column=0, sticky=tk.W, pady=20
        )
        self.station_code = tk.StringVar(value="3303A")
        station_entry = ttk.Entry(
            main_frame,
            textvariable=self.station_code,
            width=30,
            font=("Microsoft YaHei UI", 36),
        )
        station_entry.grid(row=1, column=0, sticky=tk.W + tk.E, padx=5)

        # 输入目录选择
        ttk.Label(
            main_frame, text="输入目录 (包含CSV文件的文件夹):", style="Large.TLabel"
        ).grid(row=2, column=0, sticky=tk.W, pady=20)
        self.input_path = tk.StringVar(value=current_dir)
        input_entry = ttk.Entry(
            main_frame,
            textvariable=self.input_path,
            width=30,
            font=("Microsoft YaHei UI", 36),
        )
        input_entry.grid(row=3, column=0, sticky=tk.W + tk.E, padx=5)
        ttk.Button(
            main_frame, text="浏览", command=self.browse_input, style="Large.TButton"
        ).grid(row=3, column=1, padx=20)

        # 输出目录选择
        ttk.Label(main_frame, text="输出目录:", style="Large.TLabel").grid(
            row=4, column=0, sticky=tk.W, pady=20
        )
        self.output_path = tk.StringVar(value=current_dir)
        output_entry = ttk.Entry(
            main_frame,
            textvariable=self.output_path,
            width=30,
            font=("Microsoft YaHei UI", 36),
        )
        output_entry.grid(row=5, column=0, sticky=tk.W + tk.E, padx=5)
        ttk.Button(
            main_frame, text="浏览", command=self.browse_output, style="Large.TButton"
        ).grid(row=5, column=1, padx=20)

        # 进度条
        self.progress = ttk.Progressbar(
            main_frame, length=1000, mode="determinate"
        )  # 增加进度条长度
        self.progress.grid(row=6, column=0, columnspan=2, pady=40, sticky=tk.W + tk.E)

        # 开始转换按钮
        ttk.Button(
            main_frame,
            text="开始转换",
            command=self.start_transform,
            style="Large.TButton",
        ).grid(row=7, column=0, columnspan=2, pady=30)

        # 状态标签
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        ttk.Label(main_frame, textvariable=self.status_var, style="Large.TLabel").grid(
            row=8, column=0, columnspan=2
        )

    def browse_input(self):
        directory = filedialog.askdirectory(title="选择输入目录")
        if directory:
            self.input_path.set(directory)

    def browse_output(self):
        directory = filedialog.askdirectory(title="选择输出目录")
        if directory:
            self.output_path.set(directory)

    def transform_data_shape(self, input_file):
        """将数据转换为新的形状，每个时间点对应一行，不同的环境参数作为列"""
        df = pd.read_csv(input_file)
        station_code = self.station_code.get()

        # 确保date和hour列都是字符串类型
        df["date"] = df["date"].astype(str)
        df["hour"] = df["hour"].astype(str).str.zfill(2)

        # 创建时间列
        df["datetime"] = pd.to_datetime(df["date"] + " " + df["hour"] + ":00:00")

        # 数据透视表转换
        pivot_df = df.pivot(index="datetime", columns="type", values=station_code)

        # 确保所有列名都是字符串类型
        pivot_df.columns = pivot_df.columns.astype(str)

        # 按时间顺序排序
        pivot_df.sort_index(inplace=True)

        return pivot_df

    def process_csv_files(self):
        try:
            source_dir = self.input_path.get()
            output_dir = self.output_path.get()
            station_code = self.station_code.get()

            # 获取目录下所有CSV文件
            csv_files = list(Path(source_dir).glob("*.csv"))

            if not csv_files:
                messagebox.showerror("错误", "未找到CSV文件！")
                return

            # 用于存储所有处理后的数据
            all_data = []

            # 更新进度条最大值
            self.progress["maximum"] = len(csv_files)
            self.progress["value"] = 0

            # 处理每个CSV文件
            for i, csv_file in enumerate(csv_files):
                try:
                    # 更新状态
                    self.status_var.set(f"正在处理: {csv_file.name}")
                    self.root.update()

                    # 读取CSV文件
                    df = pd.read_csv(csv_file)

                    # 提取指定站点代码的数据
                    if station_code in df.columns:
                        selected_columns = ["date", "hour", "type", station_code]
                        df_selected = df[selected_columns]
                        all_data.append(df_selected)

                    # 更新进度条
                    self.progress["value"] = i + 1
                    self.root.update()

                except Exception as e:
                    messagebox.showwarning(
                        "警告", f"处理文件 {csv_file} 时出错: {str(e)}"
                    )

            if all_data:
                # 合并所有数据
                final_df = pd.concat(all_data, ignore_index=True)

                # 保存中间文件
                temp_file = os.path.join(output_dir, f"环境数据{station_code}.csv")
                final_df.to_csv(temp_file, index=False)

                # 转换数据形状
                transformed_df = self.transform_data_shape(temp_file)
                transformed_file = os.path.join(
                    output_dir, f"transformed_环境数据{station_code}.csv"
                )
                transformed_df.to_csv(transformed_file, index=True)

                # 删除中间文件
                os.remove(temp_file)

                self.status_var.set("转换完成！")
                messagebox.showinfo("成功", f"数据已保存到: {transformed_file}")
            else:
                messagebox.showerror("错误", f"未找到站点 {station_code} 的数据")

        except Exception as e:
            messagebox.showerror("错误", f"处理过程中出错: {str(e)}")

        finally:
            self.progress["value"] = 0
            self.status_var.set("就绪")

    def start_transform(self):
        if not self.input_path.get() or not self.output_path.get():
            messagebox.showerror("错误", "请选择输入和输出目录！")
            return

        self.process_csv_files()


def main():
    root = tk.Tk()
    app = DataTransformGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
